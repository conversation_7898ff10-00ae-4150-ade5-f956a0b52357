// src/features/dashboard/components/QuickActionsGrid.tsx
import React from "react";
import {
  View,
  Text,
  Pressable,
  StyleSheet,
  Dimensions,
  Platform,
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { Ionicons, MaterialIcons } from "@expo/vector-icons";

const { width: SCREEN_WIDTH } = Dimensions.get("window");

interface Props {
  onNavigate: (route: string) => void;
}

export function QuickActionsGrid({ onNavigate }: Props) {
  const actions = [
    {
      route: "/(main)/schedule",
      icon: "calendar" as const,
      label: "לוח זמנים",
      colors: ["#FEE2E2", "#FCA5A5"],
      iconColor: "#DC2626",
    },
    {
      route: "/(main)/progress",
      icon: "trending-up" as const,
      label: "התקדמות",
      colors: ["#FEF3C7", "#FDE047"],
      iconColor: "#D97706",
      isMaterial: true,
    },
    {
      route: "/(main)/payments",
      icon: "payment" as const,
      label: "תשלומים",
      colors: ["#D1FAE5", "#6EE7B7"],
      iconColor: "#059669",
      isMaterial: true,
    },
    {
      route: "/(main)/documents",
      icon: "document-text" as const,
      label: "מסמכים",
      colors: ["#E0E7FF", "#A5B4FC"],
      iconColor: "#4338CA",
    },
  ];

  return (
    <View style={styles.container}>
      <Text style={styles.title}>פעולות מהירות</Text>
      <View style={styles.grid}>
        {actions.map((action, index) => (
          <Pressable
            key={index}
            onPress={() => onNavigate(action.route)}
            style={({ pressed }) => [
              styles.actionItem,
              { transform: [{ scale: pressed ? 0.95 : 1 }] },
            ]}
          >
            <View style={styles.iconWrapper}>
              <LinearGradient
                colors={action.colors as any}
                style={styles.iconGradient}
              >
                {action.isMaterial ? (
                  <MaterialIcons 
                    name={action.icon as any} 
                    size={24} 
                    color={action.iconColor} 
                  />
                ) : (
                  <Ionicons 
                    name={action.icon as any} 
                    size={24} 
                    color={action.iconColor} 
                  />
                )}
              </LinearGradient>
            </View>
            <Text style={styles.label}>{action.label}</Text>
          </Pressable>
        ))}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 20,
  },
  title: {
    fontSize: 18,
    fontWeight: "700",
    color: "#1F2937",
    marginBottom: 16,
  },
  grid: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 12,
  },
  actionItem: {
    width: (SCREEN_WIDTH - 52) / 4,
    alignItems: "center",
  },
  iconWrapper: {
    width: 56,
    height: 56,
    borderRadius: 16,
    overflow: "hidden",
    marginBottom: 8,
    ...Platform.select({
      ios: {
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.08,
        shadowRadius: 4,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  iconGradient: {
    width: "100%",
    height: "100%",
    justifyContent: "center",
    alignItems: "center",
  },
  label: {
    fontSize: 12,
    fontWeight: "600",
    color: "#374151",
    textAlign: "center",
  },
});