import { Stack, useRouter } from "expo-router";
import React, { useEffect, useRef, useState } from "react";
import {
  Animated,
  Platform,
  StatusBar,
  StyleSheet,
  Text,
  View,
  Pressable,
  Dimensions,
  TextInput,
  Alert,
  ActivityIndicator,
  TouchableWithoutFeedback,
  Keyboard,
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { BlurView } from "expo-blur";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import * as Haptics from "expo-haptics";
import Svg, { Path, Circle } from "react-native-svg";
import { AuthAPI, APIError, APIUtils, UserManager } from "../../services/api";
import { LocationService } from "../../src/core/services/location/LocationService";

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get("window");

export default function SignIn() {
  const insets = useSafeAreaInsets();
  const router = useRouter();
  const [phone, setPhone] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  // Animation values
  const logoScale = useRef(new Animated.Value(0.3)).current;
  const logoRotate = useRef(new Animated.Value(0)).current;
  const titleOpacity = useRef(new Animated.Value(0)).current;
  const titleTranslateY = useRef(new Animated.Value(30)).current;
  const subtitleOpacity = useRef(new Animated.Value(0)).current;
  const subtitleTranslateY = useRef(new Animated.Value(20)).current;
  const formOpacity = useRef(new Animated.Value(0)).current;
  const formTranslateY = useRef(new Animated.Value(40)).current;
  const blob1Translate = useRef(new Animated.Value(0)).current;
  const blob2Translate = useRef(new Animated.Value(0)).current;
  const buttonTranslateY = useRef(new Animated.Value(100)).current;
  const buttonOpacity = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Cascade entrance animations
    Animated.parallel([
      // Logo entrance with bounce
      Animated.spring(logoScale, {
        toValue: 1,
        tension: 20,
        friction: 7,
        delay: 200,
        useNativeDriver: true,
      }),
      // Logo subtle rotation
      Animated.timing(logoRotate, {
        toValue: 1,
        duration: 800,
        delay: 600,
        useNativeDriver: true,
      }),
      // Title animations
      Animated.parallel([
        Animated.timing(titleOpacity, {
          toValue: 1,
          duration: 800,
          delay: 400,
          useNativeDriver: true,
        }),
        Animated.spring(titleTranslateY, {
          toValue: 0,
          tension: 30,
          friction: 8,
          delay: 400,
          useNativeDriver: true,
        }),
      ]),
      // Subtitle animations
      Animated.parallel([
        Animated.timing(subtitleOpacity, {
          toValue: 1,
          duration: 800,
          delay: 600,
          useNativeDriver: true,
        }),
        Animated.spring(subtitleTranslateY, {
          toValue: 0,
          tension: 30,
          friction: 8,
          delay: 600,
          useNativeDriver: true,
        }),
      ]),
      // Form animations
      Animated.parallel([
        Animated.timing(formOpacity, {
          toValue: 1,
          duration: 800,
          delay: 800,
          useNativeDriver: true,
        }),
        Animated.spring(formTranslateY, {
          toValue: 0,
          tension: 30,
          friction: 8,
          delay: 800,
          useNativeDriver: true,
        }),
      ]),
      // Button animations
      Animated.parallel([
        Animated.spring(buttonTranslateY, {
          toValue: 0,
          tension: 30,
          friction: 8,
          delay: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(buttonOpacity, {
          toValue: 1,
          duration: 600,
          delay: 1000,
          useNativeDriver: true,
        }),
      ]),
    ]).start();

    // Floating blob animations
    const blob1Animation = Animated.loop(
      Animated.sequence([
        Animated.timing(blob1Translate, {
          toValue: 30,
          duration: 4000,
          useNativeDriver: true,
        }),
        Animated.timing(blob1Translate, {
          toValue: -30,
          duration: 4000,
          useNativeDriver: true,
        }),
      ])
    );

    const blob2Animation = Animated.loop(
      Animated.sequence([
        Animated.timing(blob2Translate, {
          toValue: -20,
          duration: 3500,
          useNativeDriver: true,
        }),
        Animated.timing(blob2Translate, {
          toValue: 20,
          duration: 3500,
          useNativeDriver: true,
        }),
      ])
    );

    blob1Animation.start();
    blob2Animation.start();

    return () => {
      blob1Animation.stop();
      blob2Animation.stop();
    };
  }, []);

  const formatPhoneDisplay = (input: string) => {
    // Remove all non-digits
    const digits = input.replace(/[^0-9]/g, '');
    
    // Format as ************
    if (digits.length <= 3) return digits;
    if (digits.length <= 6) return `${digits.slice(0, 3)}-${digits.slice(3)}`;
    return `${digits.slice(0, 3)}-${digits.slice(3, 6)}-${digits.slice(6, 10)}`;
  };

  const handlePhoneChange = (text: string) => {
    const formatted = formatPhoneDisplay(text);
    setPhone(formatted);
  };

  const handleLogin = async () => {
    if (!phone.trim()) {
      await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning);
      Alert.alert("שגיאה", "אנא הזן מספר טלפון");
      return;
    }

    // Basic phone validation
    const cleanPhone = phone.replace(/[^0-9]/g, '');
    if (cleanPhone.length < 9 || cleanPhone.length > 10) {
      await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning);
      Alert.alert("שגיאה", "מספר טלפון לא תקין");
      return;
    }

    setIsLoading(true);
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

    try {
      // Check if this is the admin user for development
      if (APIUtils.isAdminUser(phone)) {
        // Create mock admin user data
        const adminUser = {
          _id: 'dev-admin-user',
          fullName: 'Admin User',
          phone: phone,
          email: '<EMAIL>',
          isAdmin: true,
          createdAt: new Date().toISOString()
        };
        
        // Save admin user data to secure storage
        await UserManager.saveUser(adminUser);
        
        await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
        
        // Admin user bypasses instructor connection check and goes directly to dashboard
        router.replace("/(main)/dashboard");
        return;
      }
      
      const result = await AuthAPI.loginWithPhone(phone);
      
      // Save user data to secure storage
      await UserManager.saveUser(result.user);
      
      // Sync user's home location from their profile address
      try {
        await LocationService.syncUserHomeLocation();
      } catch (error) {
        console.log('Failed to sync home location:', error);
        // Don't block login if location sync fails
      }
      
      await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      
      // Check if user is connected to an instructor system
      // For now, we'll assume all users go to not-connected initially
      // Later this can be enhanced to check actual instructor connection status
      router.replace("/(auth)/not-connected");
    } catch (error) {
      await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      
      if (error instanceof APIError) {
        if (error.status === 404) {
          Alert.alert(
            "משתמש לא נמצא", 
            "לא נמצא משתמש עם מספר טלפון זה. האם ברצונך להירשם?",
            [
              { text: "ביטול", style: "cancel" },
              { 
                text: "הרשמה", 
                onPress: () => router.push("/(auth)/signup-step1")
              }
            ]
          );
        } else if (APIUtils.isNetworkError(error)) {
          Alert.alert("שגיאת חיבור", "בדוק את החיבור לאינטרנט ונסה שוב");
        } else {
          Alert.alert("שגיאה", error.message || "שגיאה בהתחברות");
        }
      } else {
        Alert.alert("שגיאה", "שגיאה בלתי צפויה");
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleBackPress = async () => {
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    router.back();
  };

  const dismissKeyboard = () => {
    Keyboard.dismiss();
  };

  return (
    <>
      <Stack.Screen options={{ headerShown: false }} />
      <StatusBar barStyle="dark-content" />
      <LinearGradient
        colors={["#EDF3FF", "#EAF1FF", "#E7EEFF"]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.container}
      >
        {/* Animated misty blobs */}
        <View style={styles.blobs} pointerEvents="none">
          <Animated.View 
            style={[
              styles.blob, 
              styles.blobA,
              {
                transform: [
                  { translateX: blob1Translate },
                  { translateY: Animated.multiply(blob1Translate, 0.5) }
                ]
              }
            ]} 
          />
          <Animated.View 
            style={[
              styles.blob, 
              styles.blobB,
              {
                transform: [
                  { translateX: blob2Translate },
                  { translateY: Animated.multiply(blob2Translate, -0.7) }
                ]
              }
            ]} 
          />
          <View style={[styles.blob, styles.blobC]} />
        </View>

        {/* Top bar with back button */}
        <View style={[styles.topBar, { paddingTop: insets.top + 10 }]}>
          <Pressable onPress={handleBackPress} style={styles.backButton}>
            <BlurView intensity={30} tint="light" style={styles.backBlur}>
              <Text style={styles.backArrow}>←</Text>
            </BlurView>
          </Pressable>
        </View>

        {/* Main content */}
        <TouchableWithoutFeedback onPress={dismissKeyboard}>
          <View style={styles.content}>
          {/* Logo with animation */}
          <Animated.View 
            style={[
              styles.logoWrap,
              {
                transform: [
                  { scale: logoScale },
                  { 
                    rotate: logoRotate.interpolate({
                      inputRange: [0, 1],
                      outputRange: ['0deg', '5deg']
                    })
                  }
                ]
              }
            ]}
          >
            <View style={styles.logoShadow} />
            <View style={styles.logoTile}>
              <Svg width={28} height={28} viewBox="0 0 24 24">
                <Path
                  d="M4 16c4-5 6 5 10-5 1.5-3 3-3 6-3"
                  stroke="#4C6BFF"
                  strokeWidth={2.8}
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  fill="none"
                />
                <Circle cx="4" cy="16" r="2.5" fill="#4C6BFF" />
                <Circle cx="20" cy="8" r="2.5" fill="#6EA8FF" />
              </Svg>
            </View>
          </Animated.View>

          {/* Animated title */}
          <Animated.View
            style={{
              opacity: titleOpacity,
              transform: [{ translateY: titleTranslateY }]
            }}
          >
            <Text style={styles.kicker}>ברוך השב</Text>
            <Text style={styles.headline}>
              היכנס <Text style={styles.headlineEm}>לחשבון</Text> שלך
            </Text>
          </Animated.View>

          {/* Animated subtitle */}
          <Animated.View
            style={{
              opacity: subtitleOpacity,
              transform: [{ translateY: subtitleTranslateY }]
            }}
          >
            <Text style={styles.sub}>
              הזן את מספר הטלפון שלך כדי להיכנס
            </Text>
          </Animated.View>

          {/* Phone input form */}
          <Animated.View
            style={[
              styles.formContainer,
              {
                opacity: formOpacity,
                transform: [{ translateY: formTranslateY }]
              }
            ]}
          >
            <View style={styles.inputContainer}>
              <View style={styles.inputWrapper}>
                <View style={styles.phoneIcon}>
                  <Text style={styles.phoneIconText}>📱</Text>
                </View>
                <TextInput
                  style={styles.phoneInput}
                  placeholder="************"
                  placeholderTextColor="rgba(0,0,0,0.4)"
                  value={phone}
                  onChangeText={handlePhoneChange}
                  keyboardType="phone-pad"
                  textAlign="right"
                  maxLength={12}
                  autoFocus={false}
                  returnKeyType="done"
                  onSubmitEditing={dismissKeyboard}
                  blurOnSubmit={true}
                />
              </View>
            </View>
          </Animated.View>
          </View>
        </TouchableWithoutFeedback>

        {/* Bottom login button */}
        <Animated.View 
          style={[
            styles.bottomSection,
            { 
              paddingBottom: insets.bottom + 24,
              opacity: buttonOpacity,
              transform: [{ translateY: buttonTranslateY }]
            }
          ]}
        >
          {/* Login button */}
          <Pressable
            onPress={handleLogin}
            disabled={isLoading}
            style={({ pressed }) => [
              styles.ctaWrap,
              { 
                transform: [{ scale: pressed ? 0.97 : 1 }],
                opacity: isLoading ? 0.7 : 1
              },
            ]}
          >
            {({ pressed }) => (
              <LinearGradient
                colors={pressed ? ["#3A52E3", "#5E98F3"] : ["#445EFB", "#6EA8FF"]}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
                style={styles.ctaFill}
              >
                {isLoading ? (
                  <ActivityIndicator color="#FFFFFF" size="small" />
                ) : (
                  <>
                    <Text style={styles.ctaText}>היכנס</Text>
                    <View style={styles.ctaArrow}>
                      <Text style={styles.ctaArrowText}>→</Text>
                    </View>
                  </>
                )}
              </LinearGradient>
            )}
          </Pressable>

          {/* Sign up prompt */}
          <View style={styles.signupPrompt}>
            <Text style={styles.signupText}>
              אין לך חשבון?{" "}
              <Pressable onPress={() => router.push("/(auth)/signup-step1")}>
                <Text style={styles.signupLink}>הירשם כאן</Text>
              </Pressable>
            </Text>
          </View>

          {/* Bottom hint */}
          <Text style={styles.bottomHint}>
            נהיגה טובה מתחילה כאן
          </Text>
        </Animated.View>
      </LinearGradient>
    </>
  );
}

const styles = StyleSheet.create({
  container: { 
    flex: 1 
  },
  
  // Animated blobs
  blobs: { 
    position: "absolute", 
    width: "100%", 
    height: "100%" 
  },
  blob: {
    position: "absolute",
    borderRadius: 999,
    backgroundColor: "#ffffff",
    ...Platform.select({
      ios: { 
        shadowColor: "#4C6BFF", 
        shadowOpacity: 0.15, 
        shadowRadius: 60,
        shadowOffset: { width: 0, height: 20 }
      },
      android: { elevation: 0 },
    }),
  },
  blobA: { 
    top: -80, 
    left: -60,
    width: 260,
    height: 260,
    opacity: 0.45,
  },
  blobB: { 
    bottom: 100, 
    right: -80, 
    opacity: 0.35,
    width: 280,
    height: 280,
  },
  blobC: { 
    top: SCREEN_HEIGHT * 0.35, 
    left: -120, 
    opacity: 0.25, 
    width: 320, 
    height: 320,
  },

  // Top bar
  topBar: {
    width: "100%",
    paddingHorizontal: 22,
    alignItems: "flex-start",
  },
  backButton: {
    borderRadius: 20,
    overflow: "hidden",
  },
  backBlur: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    width: 40,
    height: 40,
    backgroundColor: "rgba(255,255,255,0.75)",
    borderWidth: StyleSheet.hairlineWidth,
    borderColor: "rgba(0,0,0,0.08)",
  },
  backArrow: { 
    fontSize: 18,
    color: "#445EFB",
    fontWeight: "700",
  },

  // Main content
  content: {
    flex: 1,
    alignItems: "center",
    paddingHorizontal: 28,
    justifyContent: "center",
    marginTop: -60,
  },

  // Logo
  logoWrap: { 
    marginBottom: 32,
  },
  logoShadow: {
    position: "absolute",
    width: 80,
    height: 80,
    borderRadius: 20,
    backgroundColor: "#4C6BFF",
    opacity: 0.15,
    top: 6,
    alignSelf: "center",
    ...Platform.select({
      ios: {
        shadowColor: "#4C6BFF",
        shadowOpacity: 0.3,
        shadowRadius: 30,
        shadowOffset: { width: 0, height: 15 }
      },
      android: { elevation: 0 },
    }),
  },
  logoTile: {
    width: 72,
    height: 72,
    borderRadius: 20,
    backgroundColor: "#ffffff",
    alignItems: "center",
    justifyContent: "center",
    ...Platform.select({
      ios: {
        shadowColor: "#000",
        shadowOpacity: 0.12,
        shadowRadius: 16,
        shadowOffset: { width: 0, height: 8 },
      },
      android: { elevation: 8 },
    }),
  },

  // Typography
  kicker: {
    color: "#5873FF",
    fontSize: 14,
    fontWeight: "800",
    letterSpacing: 1.2,
    textAlign: "center",
    marginBottom: 12,
  },
  headline: {
    fontSize: 38,
    fontWeight: "800",
    color: "#0B0B0B",
    textAlign: "center",
    lineHeight: 46,
  },
  headlineEm: { 
    fontWeight: "900",
    color: "#445EFB",
  },
  sub: {
    fontSize: 16,
    color: "rgba(0,0,0,0.65)",
    marginTop: 12,
    textAlign: "center",
    lineHeight: 24,
    paddingHorizontal: 20,
  },

  // Form
  formContainer: {
    width: "100%",
    marginTop: 32,
  },
  inputContainer: {
    marginBottom: 24,
  },
  inputWrapper: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#FFFFFF",
    borderRadius: 16,
    borderWidth: 1.5,
    borderColor: "#E5E7EB",
    paddingHorizontal: 16,
    height: 56,
    ...Platform.select({
      ios: {
        shadowColor: "#000",
        shadowOpacity: 0.05,
        shadowRadius: 8,
        shadowOffset: { width: 0, height: 4 },
      },
      android: { elevation: 2 },
    }),
  },
  phoneIcon: {
    marginRight: 12,
  },
  phoneIconText: {
    fontSize: 20,
  },
  phoneInput: {
    flex: 1,
    fontSize: 16,
    fontWeight: "600",
    color: "#0B0B0B",
    paddingVertical: 0,
  },

  // Bottom section
  bottomSection: {
    paddingHorizontal: 22,
  },

  // Login button
  ctaWrap: {
    width: "100%",
    borderRadius: 16,
    overflow: "hidden",
    marginBottom: 16,
    ...Platform.select({
      ios: {
        shadowColor: "#445EFB",
        shadowOpacity: 0.3,
        shadowRadius: 20,
        shadowOffset: { width: 0, height: 10 },
      },
      android: { elevation: 8 },
    }),
  },
  ctaFill: { 
    paddingVertical: 18, 
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "center",
    gap: 8,
  },
  ctaText: {
    color: "#FFFFFF",
    fontSize: 17,
    fontWeight: "800",
    letterSpacing: 0.3,
  },
  ctaArrow: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: "rgba(255,255,255,0.25)",
    alignItems: "center",
    justifyContent: "center",
  },
  ctaArrowText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "700",
  },

  // Sign up prompt
  signupPrompt: {
    alignItems: "center",
    marginBottom: 16,
  },
  signupText: {
    fontSize: 14,
    color: "rgba(0,0,0,0.65)",
    textAlign: "center",
  },
  signupLink: {
    color: "#445EFB",
    fontWeight: "700",
    textDecorationLine: "underline",
  },

  // Bottom hint
  bottomHint: {
    textAlign: "center",
    fontSize: 12,
    color: "rgba(0,0,0,0.4)",
    marginTop: 8,
    fontWeight: "600",
    letterSpacing: 0.3,
  },
});