import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Location from 'expo-location';
import { SavedLocation, Address } from '../../types/location.types';
import { UserManager } from '../../../../services/api';

const STORAGE_KEY = '@saved_locations';
const DEFAULT_REGION = {
  latitude: 32.0853,
  longitude: 34.7818,
  latitudeDelta: 0.0922,
  longitudeDelta: 0.0421,
};

export class LocationService {
  static async getCurrentLocation() {
    const { status } = await Location.requestForegroundPermissionsAsync();
    if (status !== 'granted') {
      throw new Error('Location permission denied');
    }

    const location = await Location.getCurrentPositionAsync({
      accuracy: Location.Accuracy.High,
    });

    return {
      latitude: location.coords.latitude,
      longitude: location.coords.longitude,
    };
  }

  static async getSavedLocations(): Promise<SavedLocation[]> {
    try {
      const data = await AsyncStorage.getItem(STORAGE_KEY);
      let locations: SavedLocation[] = data ? JSON.parse(data) : [];
      
      // Check if user has a home address from signup that's not in saved locations
      const homeLocation = await this.getUserHomeLocation();
      if (homeLocation) {
        const hasHome = locations.some(loc => loc.type === 'home');
        if (!hasHome) {
          locations.unshift(homeLocation); // Add to beginning as default
          await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(locations));
        }
      }
      
      return locations;
    } catch {
      return [];
    }
  }

  static async saveLocation(location: SavedLocation): Promise<void> {
    const locations = await this.getSavedLocations();
    const updated = [...locations, location];
    await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(updated));
  }

  static async deleteLocation(id: string): Promise<void> {
    const locations = await this.getSavedLocations();
    const filtered = locations.filter(loc => loc.id !== id);
    await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(filtered));
  }

  static async setDefaultLocation(id: string): Promise<void> {
    const locations = await this.getSavedLocations();
    const updated = locations.map(loc => ({
      ...loc,
      isDefault: loc.id === id,
    }));
    await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(updated));
  }

  static async reverseGeocode(coords: { latitude: number; longitude: number }) {
    const result = await Location.reverseGeocodeAsync(coords);
    if (result.length > 0) {
      const addr = result[0];
      return `${addr.street || ''} ${addr.streetNumber || ''}, ${addr.city || ''}`.trim();
    }
    return 'מיקום לא ידוע';
  }

  /**
   * Get user's home location from their profile address
   */
  static async getUserHomeLocation(): Promise<SavedLocation | null> {
    try {
      const user = await UserManager.getUser();
      if (!user?.address) {
        return null;
      }

      const address = user.address;
      const fullAddress = this.formatAddress(address);
      
      // Try to geocode the address to get coordinates
      const coords = await this.geocodeAddress(address);
      
      return {
        id: 'user-home',
        label: 'בית',
        address: fullAddress,
        coordinates: coords,
        type: 'home',
        isDefault: true,
      };
    } catch (error) {
      console.log('Failed to get user home location:', error);
      return null;
    }
  }

  /**
   * Format address object into readable string
   */
  private static formatAddress(address: Address): string {
    const { street, buildingNumber, apartment, city } = address;
    let formatted = `${street} ${buildingNumber}`;
    if (apartment) {
      formatted += `, דירה ${apartment}`;
    }
    formatted += `, ${city}`;
    return formatted;
  }

  /**
   * Convert address to coordinates using geocoding
   */
  private static async geocodeAddress(address: Address): Promise<{ latitude: number; longitude: number }> {
    try {
      const query = `${address.street} ${address.buildingNumber}, ${address.city}, Israel`;
      const results = await Location.geocodeAsync(query);
      
      if (results.length > 0) {
        return {
          latitude: results[0].latitude,
          longitude: results[0].longitude,
        };
      }
    } catch (error) {
      console.log('Geocoding failed for address:', address, error);
    }
    
    // Fallback to default region (Tel Aviv area)
    return {
      latitude: DEFAULT_REGION.latitude,
      longitude: DEFAULT_REGION.longitude,
    };
  }

  /**
   * Sync user's current address as home location
   */
  static async syncUserHomeLocation(): Promise<void> {
    const homeLocation = await this.getUserHomeLocation();
    if (homeLocation) {
      const locations = await this.getSavedLocations();
      const homeIndex = locations.findIndex(loc => loc.type === 'home');
      
      if (homeIndex >= 0) {
        // Update existing home location
        locations[homeIndex] = homeLocation;
      } else {
        // Add new home location
        locations.unshift(homeLocation);
      }
      
      await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(locations));
    }
  }
}