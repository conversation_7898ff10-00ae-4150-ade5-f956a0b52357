# DLSA Backend Setup & Troubleshooting Guide

## Overview

This document outlines the process of setting up and troubleshooting the DLSA (Driving License Student Application) backend, including MongoDB database configuration and the Node.js/TypeScript API server.

## Final Working Configuration

### Backend Status ✅
- **Server**: Running on http://localhost:3000
- **MongoDB**: Running on port 27018
- **Database**: DLSA_app
- **Environment**: Development mode
- **Authentication**: Registration-based flow (not traditional login)

## What Was Done

### 1. MongoDB Setup
- **Version**: MongoDB Server 8.0
- **Port**: 27018 (non-standard to avoid conflicts)
- **Data Directory**: `C:\mongo\dlsa_app\data`
- **Bind IP**: 127.0.0.1 (localhost only)

**Start Command:**
```bash
"/c/Program Files/MongoDB/Server/8.0/bin/mongod.exe" --port 27018 --dbpath "C:\mongo\dlsa_app\data" --bind_ip 127.0.0.1
```

### 2. Backend Environment Configuration
Created `.env` file with proper configuration:
```env
PORT=3000
MONGODB_URI=mongodb://127.0.0.1:27018/DLSA_app
MONGODB_DB=DLSA_app
DEV_ADMIN_TOKEN=dev-only-secret
CORS_ORIGIN=*
NODE_ENV=development
```

### 3. Package Dependencies
**Missing Package Resolved:**
- Added `dotenv@17.2.1` to dependencies (was imported but not installed)

### 4. Code Fixes
**Critical Fix - Main Module Detection:**
```typescript
// Before (non-working):
if (import.meta.url === `file://${process.argv[1]}`) {
  startServer();
}

// After (working):
const isMainModule = process.argv[1]?.endsWith('server.ts') || process.argv[1]?.endsWith('server.js');
if (isMainModule) {
  console.log("🚀 Starting server...");
  startServer();
}
```

## Issues Encountered & Solutions

### Issue 1: Backend Not Starting
**Problem**: Server would load environment variables but never actually start
**Root Cause**: ES module main detection logic was faulty
**Solution**: Fixed main module detection logic in `server.ts:400-404`

### Issue 2: Missing Dependencies
**Problem**: `dotenv` was imported but not installed
**Solution**: Added `dotenv` to package.json dependencies

### Issue 3: Database Configuration
**Problem**: Database name mismatch between URI and environment variable
**Solution**: Added explicit `MONGODB_DB=DLSA_app` to ensure correct database selection

### Issue 4: Silent Failures
**Problem**: Server would exit without error messages
**Solution**: Added startup logging and improved main module detection

## Architecture Understanding

### Authentication Model
This application uses a **registration-based authentication** system, not traditional login:

1. **Step 1**: User provides personal details (name, phone, email)
2. **Step 2**: User provides address information
3. **Result**: User is registered and can access the app

### API Endpoints
- `GET /healthz` - Health check
- `POST /api/registration/start` - Begin registration
- `PATCH /api/registration/:regId/complete` - Complete registration
- `GET /api/registration/:regId` - Get registration status
- `GET /api/users` - List users (admin only)

### Database Collections
- **users**: Stores user registration data with indexes on:
  - `phone` (unique)
  - `createdAt` (descending)
  - Text search on `fullName`, `email`, `address.city`

## Key Learnings

### 1. ES Module Gotchas
- `import.meta.url` comparison with `process.argv[1]` is unreliable
- Better to use filename-based detection for main module identification
- Always add debug logging for startup sequences

### 2. Environment Configuration
- Explicit database name configuration prevents ambiguity
- `.env` files need to be created even when environment variables are passed
- dotenv package is required when using `import { config } from "dotenv"`

### 3. MongoDB Connection Patterns
- Connection timeouts should be reasonable (5-10 seconds)
- Index creation should be non-blocking for startup
- Health checks should test actual database connectivity

### 4. TypeScript/Node.js Debugging
- Compile TypeScript first to catch syntax errors
- Use `npm run build && npm start` for reliable deployment
- ES modules require proper file extensions in imports (`.js` not `.ts`)

### 5. Development Workflow
- Start with basic connectivity tests before complex flows
- Test database connection independently of application logic
- Use proper HTTP status codes and error messages for API debugging

## Testing Commands

### Health Check
```bash
curl -s http://localhost:3000/healthz
```

### Complete Registration Flow
```bash
# Start registration
curl -s -X POST http://localhost:3000/api/registration/start \
  -H "Content-Type: application/json" \
  -d '{"fullName": "David Cohen", "phone": "+972501234567", "email": "<EMAIL>", "deviceId": "550e8400-e29b-41d4-a716-446655440000"}'

# Complete registration (use regId from above)
curl -s -X PATCH "http://localhost:3000/api/registration/8f144dee-e985-433d-ae65-6bb8c6c75ff1/complete" \
  -H "Content-Type: application/json" \
  -d '{"address": {"city": "תל אביב", "street": "רחוב הרצל", "buildingNumber": "123", "apartment": "5"}}'

# List users
curl -s -H "X-Admin-Token: dev-only-secret" http://localhost:3000/api/users
```

## Production Considerations

### Security
- Change `DEV_ADMIN_TOKEN` to a secure value
- Use proper CORS origins (not `*`)
- Enable MongoDB authentication
- Use HTTPS in production

### Performance
- Implement connection pooling
- Add rate limiting (already configured)
- Consider database indexing strategies for scale
- Implement proper logging levels

### Monitoring
- Add application performance monitoring
- Set up database monitoring
- Implement health check endpoints for load balancers

## Troubleshooting Guide

### Backend Won't Start
1. Check if `.env` file exists and is properly formatted
2. Verify all dependencies are installed (`npm install`)
3. Ensure MongoDB is running and accessible
4. Check for TypeScript compilation errors (`npm run build`)

### Database Connection Issues
1. Verify MongoDB is running on correct port
2. Check database name in connection string
3. Test connection manually with MongoDB client
4. Verify network connectivity to database

### Registration API Errors
1. Ensure proper JSON content-type headers
2. Validate required fields (deviceId must be UUID)
3. Check phone number format (international format preferred)
4. Verify admin token for user listing endpoint

---

**Date**: August 18, 2025  
**Status**: ✅ Backend fully operational  
**Next Steps**: Frontend integration and mobile app testing