import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  Pressable,
  Animated,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Dimensions,
} from 'react-native';
import { BlurView } from 'expo-blur';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import * as Location from 'expo-location';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { tokens } from '@/src/core/theme/tokens';

const { height: SCREEN_HEIGHT } = Dimensions.get('window');

interface Props {
  onSave: (name: string, address: string, coordinates: any) => void;
  onClose: () => void;
}

export const AddLocationFlow: React.FC<Props> = ({ onSave, onClose }) => {
  const insets = useSafeAreaInsets();
  const [step, setStep] = useState<'name' | 'address'>('name');
  const [locationName, setLocationName] = useState('');
  const [address, setAddress] = useState({
    street: '',
    buildingNumber: '',
    city: '',
  });
  
  const slideAnim = useRef(new Animated.Value(SCREEN_HEIGHT)).current;
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const contentScale = useRef(new Animated.Value(0.8)).current;

  React.useEffect(() => {
    Animated.parallel([
      Animated.spring(slideAnim, {
        toValue: 0,
        tension: 50,
        friction: 10,
        useNativeDriver: true,
      }),
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.spring(contentScale, {
        toValue: 1,
        delay: 100,
        tension: 30,
        friction: 8,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const handleClose = async () => {
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    
    Animated.parallel([
      Animated.timing(slideAnim, {
        toValue: SCREEN_HEIGHT,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }),
    ]).start(() => onClose());
  };

  const handleNext = async () => {
    if (step === 'name' && locationName.trim()) {
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      setStep('address');
    }
  };

  const handleSave = async () => {
    if (!locationName.trim() || !address.street.trim() || !address.city.trim()) {
      return;
    }

    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    
    const fullAddress = `${address.street} ${address.buildingNumber}, ${address.city}`;
    
    try {
      // Geocode the address
      const results = await Location.geocodeAsync(`${fullAddress}, Israel`);
      const coordinates = results[0] || { latitude: 32.0853, longitude: 34.7818 };
      
      onSave(locationName, fullAddress, coordinates);
    } catch (error) {
      // Use default Tel Aviv coordinates as fallback
      onSave(locationName, fullAddress, { latitude: 32.0853, longitude: 34.7818 });
    }
  };

  const renderNameStep = () => (
    <Animated.View
      style={[
        styles.stepContainer,
        { transform: [{ scale: contentScale }] },
      ]}
    >
      <View style={styles.stepHeader}>
        <View style={styles.stepIndicator}>
          <Text style={styles.stepNumber}>1</Text>
        </View>
        <Text style={styles.stepTitle}>תן שם למיקום</Text>
      </View>
      
      <TextInput
        style={styles.nameInput}
        placeholder="לדוגמה: בית של סבתא"
        placeholderTextColor={tokens.colors.textTertiary}
        value={locationName}
        onChangeText={setLocationName}
        autoFocus
        returnKeyType="next"
        onSubmitEditing={handleNext}
      />
      
      <View style={styles.suggestions}>
        {['בית', 'עבודה', 'חדר כושר', 'בית ספר'].map((suggestion) => (
          <Pressable
            key={suggestion}
            onPress={() => setLocationName(suggestion)}
            style={({ pressed }) => [
              styles.suggestionChip,
              pressed && styles.suggestionPressed,
            ]}
          >
            <Text style={styles.suggestionText}>{suggestion}</Text>
          </Pressable>
        ))}
      </View>
    </Animated.View>
  );

  const renderAddressStep = () => (
    <Animated.View
      style={[
        styles.stepContainer,
        { transform: [{ scale: contentScale }] },
      ]}
    >
      <View style={styles.stepHeader}>
        <View style={[styles.stepIndicator, styles.stepIndicatorActive]}>
          <Text style={styles.stepNumber}>2</Text>
        </View>
        <Text style={styles.stepTitle}>הכנס כתובת</Text>
      </View>
      
      <View style={styles.addressInputs}>
        <TextInput
          style={styles.addressInput}
          placeholder="עיר"
          placeholderTextColor={tokens.colors.textTertiary}
          value={address.city}
          onChangeText={(text) => setAddress({ ...address, city: text })}
          autoFocus
          returnKeyType="next"
        />
        
        <View style={styles.addressRow}>
          <TextInput
            style={[styles.addressInput, styles.addressInputFlex]}
            placeholder="רחוב"
            placeholderTextColor={tokens.colors.textTertiary}
            value={address.street}
            onChangeText={(text) => setAddress({ ...address, street: text })}
            returnKeyType="next"
          />
          
          <TextInput
            style={[styles.addressInput, styles.addressInputSmall]}
            placeholder="מספר"
            placeholderTextColor={tokens.colors.textTertiary}
            value={address.buildingNumber}
            onChangeText={(text) => setAddress({ ...address, buildingNumber: text })}
            keyboardType="numeric"
            returnKeyType="done"
            onSubmitEditing={handleSave}
          />
        </View>
      </View>
    </Animated.View>
  );

  return (
    <View style={StyleSheet.absoluteFillObject}>
      {/* Backdrop */}
      <Animated.View
        style={[
          styles.backdrop,
          { opacity: fadeAnim },
        ]}
      >
        <Pressable style={styles.backdropPress} onPress={handleClose} />
      </Animated.View>

      {/* Content */}
      <Animated.View
        style={[
          styles.container,
          {
            transform: [{ translateY: slideAnim }],
            paddingTop: insets.top,
            paddingBottom: insets.bottom + 20,
          },
        ]}
      >
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={styles.keyboardView}
        >
          <BlurView intensity={98} tint="light" style={styles.content}>
            {/* Header */}
            <View style={styles.header}>
              <Pressable onPress={handleClose} style={styles.closeButton}>
                <Ionicons name="close" size={28} color={tokens.colors.textSecondary} />
              </Pressable>
              <Text style={styles.title}>מיקום חדש</Text>
              <View style={styles.closeButton} />
            </View>

            {/* Progress Bar */}
            <View style={styles.progressBar}>
              <View
                style={[
                  styles.progressFill,
                  { width: step === 'name' ? '50%' : '100%' },
                ]}
              />
            </View>

            {/* Steps */}
            <ScrollView
              style={styles.scrollContent}
              showsVerticalScrollIndicator={false}
              keyboardShouldPersistTaps="handled"
            >
              {step === 'name' ? renderNameStep() : renderAddressStep()}
            </ScrollView>

            {/* Actions */}
            <View style={styles.actions}>
              {step === 'address' && (
                <Pressable
                  onPress={() => setStep('name')}
                  style={({ pressed }) => [
                    styles.backButton,
                    pressed && styles.buttonPressed,
                  ]}
                >
                  <Ionicons name="chevron-back" size={20} color={tokens.colors.textSecondary} />
                  <Text style={styles.backText}>חזור</Text>
                </Pressable>
              )}
              
              <Pressable
                onPress={step === 'name' ? handleNext : handleSave}
                disabled={
                  step === 'name' 
                    ? !locationName.trim()
                    : !address.street.trim() || !address.city.trim()
                }
                style={({ pressed }) => [
                  styles.primaryButton,
                  pressed && styles.buttonPressed,
                  (step === 'name' ? !locationName.trim() : !address.street.trim() || !address.city.trim()) && styles.buttonDisabled,
                ]}
              >
                <LinearGradient
                  colors={[tokens.colors.primary, tokens.colors.primaryLight]}
                  style={styles.primaryButtonGradient}
                >
                  <Text style={styles.primaryButtonText}>
                    {step === 'name' ? 'המשך' : 'שמור מיקום'}
                  </Text>
                  <Ionicons 
                    name={step === 'name' ? 'arrow-forward' : 'checkmark'} 
                    size={20} 
                    color="#FFFFFF" 
                  />
                </LinearGradient>
              </Pressable>
            </View>
          </BlurView>
        </KeyboardAvoidingView>
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  backdrop: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  backdropPress: {
    flex: 1,
  },
  container: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 0,
    height: SCREEN_HEIGHT * 0.75,
  },
  keyboardView: {
    flex: 1,
  },
  content: {
    flex: 1,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    overflow: 'hidden',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: tokens.colors.border,
  },
  closeButton: {
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  title: {
    fontSize: 18,
    fontWeight: '700',
    color: tokens.colors.textPrimary,
  },
  progressBar: {
    height: 3,
    backgroundColor: tokens.colors.border,
  },
  progressFill: {
    height: '100%',
    backgroundColor: tokens.colors.primary,
    transition: 'width 0.3s ease',
  },
  scrollContent: {
    flex: 1,
    padding: 20,
  },
  stepContainer: {
    paddingVertical: 20,
  },
  stepHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
  },
  stepIndicator: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: tokens.colors.backgroundSurface,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  stepIndicatorActive: {
    backgroundColor: tokens.colors.primary,
  },
  stepNumber: {
    fontSize: 16,
    fontWeight: '700',
    color: tokens.colors.textPrimary,
  },
  stepTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: tokens.colors.textPrimary,
  },
  nameInput: {
    backgroundColor: tokens.colors.backgroundSurface,
    borderRadius: tokens.borderRadius.lg,
    paddingHorizontal: 20,
    paddingVertical: 16,
    fontSize: 18,
    fontWeight: '500',
    color: tokens.colors.textPrimary,
    marginBottom: 20,
  },
  suggestions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  suggestionChip: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: tokens.colors.backgroundSurface,
    borderWidth: 1,
    borderColor: tokens.colors.border,
  },
  suggestionPressed: {
    opacity: 0.7,
  },
  suggestionText: {
    fontSize: 14,
    fontWeight: '600',
    color: tokens.colors.textSecondary,
  },
  addressInputs: {
    gap: 12,
  },
  addressRow: {
    flexDirection: 'row',
    gap: 12,
  },
  addressInput: {
    backgroundColor: tokens.colors.backgroundSurface,
    borderRadius: tokens.borderRadius.lg,
    paddingHorizontal: 20,
    paddingVertical: 16,
    fontSize: 16,
    fontWeight: '500',
    color: tokens.colors.textPrimary,
  },
  addressInputFlex: {
    flex: 1,
  },
  addressInputSmall: {
    width: 100,
  },
  actions: {
    flexDirection: 'row',
    gap: 12,
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderTopWidth: StyleSheet.hairlineWidth,
    borderTopColor: tokens.colors.border,
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  backText: {
    fontSize: 16,
    fontWeight: '600',
    color: tokens.colors.textSecondary,
  },
  primaryButton: {
    flex: 1,
    borderRadius: tokens.borderRadius.lg,
    overflow: 'hidden',
  },
  primaryButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
    paddingVertical: 16,
  },
  primaryButtonText: {
    fontSize: 16,
    fontWeight: '700',
    color: '#FFFFFF',
  },
  buttonPressed: {
    opacity: 0.8,
  },
  buttonDisabled: {
    opacity: 0.5,
  },
});