import React, { useRef, useState } from 'react';
import {
  Pressable,
  StyleSheet,
  Animated,
  View,
  Text,
  ViewStyle,
  Platform
} from 'react-native';
import * as Haptics from 'expo-haptics';
import { tokens } from '../../core/theme/tokens';

interface SwitchProps {
  value: boolean;
  onValueChange: (value: boolean) => void;
  label?: string;
  disabled?: boolean;
  size?: 'small' | 'medium' | 'large';
  style?: ViewStyle;
}

export const Switch: React.FC<SwitchProps> = ({
  value,
  onValueChange,
  label,
  disabled = false,
  size = 'medium',
  style,
}) => {
  const translateX = useRef(new Animated.Value(value ? 1 : 0)).current;

  const handlePress = async () => {
    if (disabled) return;
    
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    
    const newValue = !value;
    onValueChange(newValue);
    
    Animated.spring(translateX, {
      toValue: newValue ? 1 : 0,
      useNativeDriver: true,
    }).start();
  };

  const knobTranslateX = translateX.interpolate({
    inputRange: [0, 1],
    outputRange: [2, sizeStyles[size].translateX],
  });

  const trackColor = translateX.interpolate({
    inputRange: [0, 1],
    outputRange: ['#E5E7EB', tokens.colors.primary],
  });

  return (
    <Pressable
      onPress={handlePress}
      disabled={disabled}
      style={[styles.container, disabled && styles.disabled, style]}
    >
      {label && <Text style={styles.label}>{label}</Text>}
      <Animated.View style={[
        styles.track,
        sizeStyles[size].track,
        { backgroundColor: trackColor },
      ]}>
        <Animated.View style={[
          styles.knob,
          sizeStyles[size].knob,
          { transform: [{ translateX: knobTranslateX }] },
        ]} />
      </Animated.View>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    color: tokens.colors.textPrimary,
    marginRight: 12,
  },
  track: {
    borderRadius: 999,
    justifyContent: 'center',
  },
  knob: {
    backgroundColor: tokens.colors.backgroundPure,
    borderRadius: 999,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.15,
        shadowRadius: 3,
      },
      android: {
        elevation: 3,
      },
    }),
  },
  disabled: {
    opacity: 0.5,
  },
});

const sizeStyles = {
  small: {
    track: { width: 36, height: 20 },
    knob: { width: 16, height: 16 },
    translateX: 18,
  },
  medium: {
    track: { width: 48, height: 28 },
    knob: { width: 24, height: 24 },
    translateX: 22,
  },
  large: {
    track: { width: 60, height: 34 },
    knob: { width: 30, height: 30 },
    translateX: 28,
  },
};