import { z } from "zod";

/**
 * Validation schemas for API endpoints
 */

// Phone number validation - supports Israeli format
const phoneRegex = /^\+?972[0-9]{8,9}$|^0[0-9]{8,9}$|^[0-9]{9,10}$/;

export const AddressSchema = z.object({
  city: z.string().min(1, "City is required").max(50, "City too long"),
  street: z.string().min(1, "Street is required").max(100, "Street too long"),
  buildingNumber: z
    .string()
    .min(1, "Building number is required")
    .max(10, "Building number too long"),
  apartment: z.string().max(10, "Apartment too long").optional(),
});

export const RegistrationStartSchema = z.object({
  fullName: z
    .string()
    .min(2, "Full name must be at least 2 characters")
    .max(100, "Full name too long")
    .regex(/^[a-zA-Z\u0590-\u05FF\s]+$/, "Invalid characters in name"),
  phone: z.string().regex(phoneRegex, "Invalid phone number format"),
  email: z.string().email("Invalid email format").optional().or(z.literal("")),
  deviceId: z.string().uuid("Invalid device ID format"),
});

export const RegistrationCompleteSchema = z.object({
  address: AddressSchema,
});

export const UserListQuerySchema = z.object({
  q: z.string().optional(),
  limit: z.string().regex(/^\d+$/).optional(),
  cursor: z.string().optional(),
});

export const RegIdParamSchema = z.object({
  regId: z.string().uuid("Invalid registration ID format"),
});

// Helper function to normalize phone numbers to E.164 format
export function normalizePhone(phone: string): string {
  // Remove all non-digit characters
  const digits = phone.replace(/\D/g, "");

  // Add country code if missing
  if (digits.startsWith("972")) {
    return `+${digits}`;
  } else if (digits.startsWith("0")) {
    return `+972${digits.slice(1)}`;
  } else if (digits.length >= 9) {
    return `+972${digits}`;
  }

  return `+972${digits}`;
}

// Helper function to validate and normalize input data
export function validateAndNormalizeRegistrationStart(data: any) {
  const validated = RegistrationStartSchema.parse(data);

  return {
    ...validated,
    phone: normalizePhone(validated.phone),
    email: validated.email || undefined,
  };
}
