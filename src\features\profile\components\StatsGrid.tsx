// src/features/profile/components/StatsGrid.tsx
import React from 'react';
import { View, StyleSheet, Dimensions } from 'react-native';
import { StatCard } from '@/src/components';
import { UserStats } from '../types';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

interface Props {
  stats?: UserStats;
}

export function StatsGrid({ stats }: Props) {
  // Default values if stats is undefined
  const safeStats = stats || {
    lessonsCompleted: 0,
    hoursCompleted: 0,
    upcomingLessons: 0,
    averageRating: 0,
  };

  return (
    <View style={styles.container}>
      <StatCard
        label="שיעורים"
        value={safeStats.lessonsCompleted}
        icon="school-outline"
        trend={safeStats.lessonsCompleted > 20 ? 'up' : 'neutral'}
        trendValue={safeStats.lessonsCompleted > 20 ? '+12%' : undefined}
        variant="gradient"
        style={styles.card}
      />
      <StatCard
        label="שעות נהיגה"
        value={`${safeStats.hoursCompleted}h`}
        icon="time-outline"
        variant="default"
        style={styles.card}
      />
      <StatCard
        label="קרובים"
        value={safeStats.upcomingLessons}
        subValue="שיעורים"
        icon="calendar-outline"
        variant="default"
        style={styles.card}
      />
      <StatCard
        label="דירוג"
        value={safeStats.averageRating.toFixed(1)}
        icon="star"
        variant="gradient"
        style={styles.card}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    marginBottom: 20,
  },
  card: {
    width: (SCREEN_WIDTH - 52) / 2,
  },
});