# **🎨 Design System Guide \- Driving Lesson App**

## **Design Philosophy**

### **Core Principle: "Floating Elegance"**

This design language combines **Apple's minimalism** with **subtle dynamism** \- creating interfaces that feel alive but never overwhelming. Every element has purpose, every animation has meaning, and every interaction feels premium.

---

## **🎭 Design Personality**

### **The Feeling We Want to Evoke:**

* **Professional yet Approachable** \- Like a high-end service that's still friendly  
* **Calm Confidence** \- Users should feel they're in good hands  
* **Subtle Sophistication** \- Premium without being intimidating  
* **Gentle Movement** \- The app breathes and flows, never static  
* **Trust & Reliability** \- Clean, organized, predictable

### **Visual Metaphors:**

* **Floating** \- Elements gently drift, creating depth  
* **Breathing** \- Subtle scale animations make the app feel alive  
* **Layering** \- Blur effects and shadows create spatial hierarchy  
* **Soft Light** \- Gradients and glows suggest optimism and progress

---

## **🎨 Color System**

### **Primary Palette**

Main Blue:       \#445EFB \- Primary actions, brand identity  
Light Blue:      \#6EA8FF \- Accents, secondary elements  
Deep Blue:       \#3A52E3 \- Pressed states, emphasis  
Soft Blue:       \#5873FF \- Text accents, links

### **Background System**

Light Gradient Start: \#EDF3FF  
Light Gradient Mid:   \#EAF1FF    
Light Gradient End:   \#E7EEFF  
Pure White:          \#FFFFFF \- Cards, tiles  
Soft White:          rgba(255,255,255,0.82) \- Blurred overlays

### **Text Colors**

Primary Text:    \#0B0B0B \- Headlines, important text  
Secondary Text:  rgba(0,0,0,0.75) \- Body text  
Tertiary Text:   rgba(0,0,0,0.65) \- Subtitles  
Muted Text:      rgba(0,0,0,0.4) \- Hints, captions  
White Text:      \#FFFFFF \- On colored backgrounds

### **Semantic Colors**

Success:  \#10B981 \- Completion, success states  
Warning:  \#F59E0B \- Alerts, important notices  
Error:    \#EF4444 \- Errors, destructive actions  
Info:     \#4C6BFF \- Information, tips

---

## **📐 Layout Principles**

### **Spatial Hierarchy**

1. **Hero Space** (Top 35-40% of screen)

   * Logo or main visual  
   * Primary heading  
   * Key message  
2. **Content Zone** (Middle 30-35%)

   * Supporting information  
   * Features or benefits  
   * Interactive elements  
3. **Action Zone** (Bottom 25-30%)

   * Primary CTA button  
   * Secondary actions  
   * Legal/hint text

### **Spacing System**

Base unit: 4px

Micro:    4px  \- Tiny gaps  
Small:    8px  \- Icon to text  
Medium:   12px \- Between related elements  
Large:    16px \- Between sections  
XLarge:   24px \- Major sections  
XXLarge:  32px \- Page margins  
Huge:     48px \- Hero spacing

### **Component Positioning Rules**

* **Floating Elements**: 10-15% offset from edges  
* **Buttons**: Always anchored to bottom with 24px padding  
* **Cards**: Centered with 22-28px horizontal margins  
* **Overlays**: Full width with 20px margins

---

## **🔤 Typography**

### **Font Hierarchy**

// Headlines  
.headline-hero {  
  font-size: 38px;  
  font-weight: 800;  
  line-height: 46px;  
  letter-spacing: \-0.5px;  
}

// Subheadings  
.subheading {  
  font-size: 20px;  
  font-weight: 700;  
  line-height: 28px;  
}

// Body Text  
.body-main {  
  font-size: 16px;  
  font-weight: 400;  
  line-height: 24px;  
}

// Button Text  
.button-primary {  
  font-size: 17px;  
  font-weight: 800;  
  letter-spacing: 0.3px;  
}

// Caption/Kicker  
.caption {  
  font-size: 14px;  
  font-weight: 800;  
  letter-spacing: 1.2px;  
  text-transform: uppercase;  
}

// Hints  
.hint {  
  font-size: 12px;  
  font-weight: 600;  
  letter-spacing: 0.3px;  
}

### **Hebrew/English Rules**

* Hebrew text: Slightly larger (1-2px) for better readability  
* English subtitles: Lighter weight, smaller size  
* Mixed text: Hebrew leads, English follows in lighter shade

---

## **🎬 Animation Principles**

### **Timing Functions**

// Entrance animations  
spring: {  
  tension: 30,  
  friction: 8  
}

// Smooth transitions  
timing: {  
  duration: 600-800ms,  
  easing: cubic-bezier(0.4, 0, 0.2, 1\)  
}

// Micro interactions  
quick: {  
  duration: 200-300ms  
}

### **Animation Patterns**

#### **1\. Cascade Entry (Screen Load)**

* Elements appear in sequence  
* 200ms delay between elements  
* Bottom-to-top or center-outward  
* Combined with fade \+ slide

#### **2\. Floating Animation (Ambient)**

// Continuous gentle movement  
amplitude: 20-30px  
duration: 3500-4000ms  
loop: true  
easing: sine-wave

#### **3\. Press Feedback**

scale: 0.97-0.98 (buttons)  
scale: 0.95 (cards)  
duration: 150ms  
haptic: Light or Medium

#### **4\. Page Transitions**

* **Forward**: Slide from right \+ fade  
* **Back**: Slide to right \+ fade  
* **Modal**: Slide from bottom \+ blur background  
* Duration: 300-400ms

---

## **🎯 Interactive Elements**

### **Button Hierarchy**

#### **Primary Button**

Height: 56-60px  
Border Radius: 16px  
Background: Linear Gradient (\#445EFB → \#6EA8FF)  
Shadow: 0px 10px 20px rgba(68, 94, 251, 0.3)  
Press: Scale 0.97, Darker gradient  
Text: White, 17px, Weight 800

#### **Secondary Button**

Height: 56px  
Border Radius: 16px  
Background: Blur white (82% opacity)  
Border: 1px rgba(0,0,0,0.06)  
Shadow: Subtle (0px 4px 10px rgba(0,0,0,0.06))  
Press: Scale 0.98  
Text: Dark (75% black), 16px, Weight 700

#### **Ghost Button**

Height: 48px  
Background: None  
Text: Primary blue  
Press: Background rgba(68, 94, 251, 0.08)

### **Cards & Tiles**

Border Radius: 16-20px  
Background: White or Blur  
Shadow: 0px 8px 16px rgba(0,0,0,0.12)  
Padding: 20-24px  
Float animation: Optional subtle Y-axis movement

---

## **🌟 Special Effects**

### **Glass Morphism (Blur Effects)**

Intensity: 20-30 for overlays  
Intensity: 10-15 for subtle elements  
Tint: "light" for most cases  
Background: rgba(255,255,255,0.65-0.82)  
Border: 1px rgba(0,0,0,0.06-0.08)

### **Floating Blobs (Background)**

Size: 260-320px diameter  
Opacity: 0.18-0.45  
Color: White  
Shadow: Colored glow (shadowColor: \#4C6BFF)  
Animation: Slow drift (3500-4000ms)  
Blur radius: 60px shadow

### **Shadows**

// Elevation levels  
.elevation-1 {  
  shadow: 0px 1px 2px rgba(0,0,0,0.05);  
}

.elevation-2 {  
  shadow: 0px 4px 8px rgba(0,0,0,0.08);  
}

.elevation-3 {  
  shadow: 0px 8px 16px rgba(0,0,0,0.12);  
}

.elevation-hero {  
  shadow: 0px 10px 30px rgba(68,94,251,0.3);  
}

---

## **📱 Component Patterns**

### **Screen Structure Template**

\<Screen\>  
  {/\* Background Layer \*/}  
  \<LinearGradient /\>  
  \<FloatingBlobs /\>  
    
  {/\* Content Layer \*/}  
  \<SafeArea\>  
    \<TopBar /\>        // Optional language/settings  
    \<HeroSection /\>   // Logo, main title  
    \<ContentBody /\>   // Main content  
    \<ActionZone /\>    // Fixed bottom buttons  
  \</SafeArea\>  
\</Screen\>

### **Logo Presentation**

* Size: 72x72px (hero), 56x56px (standard)  
* Border Radius: 20px  
* Background: Pure white  
* Shadow: Multi-layered (glow \+ drop shadow)  
* Animation: Scale \+ subtle rotation on entry  
* Icon: Centered, 60% of tile size

### **Form Fields**

Height: 56px  
Border Radius: 16px  
Background: White  
Border: 1.5px \#E5E7EB (default), \#445EFB (focused)  
Padding: 16px horizontal  
Icon: 20px, left side  
Focus: Blue border \+ shadow  
Error: Red border  
Animation: Border color transition 200ms

---

## **🎯 Key Design Rules**

### **Do's ✅**

1. **Always animate entrances** \- Nothing appears instantly  
2. **Use cascading delays** \- Create visual hierarchy through timing  
3. **Add micro-interactions** \- Every touch should respond  
4. **Layer shadows** \- Create depth with multiple shadow levels  
5. **Blur backgrounds** \- Use glass morphism for overlays  
6. **Keep text hierarchical** \- Clear size/weight differences  
7. **Anchor actions** \- CTAs at bottom, always accessible  
8. **Add breathing room** \- Generous whitespace  
9. **Use gradients subtly** \- Enhance, don't overwhelm  
10. **Include haptic feedback** \- Physical response to actions

### **Don'ts ❌**

1. **No harsh transitions** \- Always smooth  
2. **Avoid pure black** \- Use \#0B0B0B or opacity  
3. **No instant appearances** \- Everything fades/slides in  
4. **Don't overcrowd** \- Maximum 3 actions per screen  
5. **Avoid sharp corners** \- Minimum 12px radius  
6. **No static screens** \- Something should always gently move  
7. **Don't use system fonts** \- Always specify weights  
8. **Avoid centered everything** \- Create visual flow  
9. **No aggressive animations** \- Keep it subtle  
10. **Don't forget platform differences** \- iOS shadows vs Android elevation

---

## **🚀 Implementation Checklist**

For every new screen, ensure:

* \[ \] Background has gradient \+ floating elements  
* \[ \] Logo/hero element has entrance animation  
* \[ \] Text appears in cascade (title → subtitle → body)  
* \[ \] Buttons are anchored to bottom  
* \[ \] Primary action has gradient \+ shadow  
* \[ \] Touch feedback includes haptics  
* \[ \] Blur effects on overlays  
* \[ \] Safe area insets respected  
* \[ \] RTL support for Hebrew  
* \[ \] Loading states have animations  
* \[ \] Error states are gentle, not alarming  
* \[ \] Success feedback is celebratory but professional

---

## **📝 Example Component Recipe**

### **"Perfect Welcome Screen"**

1. **Background**: Light blue gradient (\#EDF3FF → \#E7EEFF)  
2. **Floating blobs**: 3 white circles, different sizes, animated drift  
3. **Logo**: 72px tile, white, centered at 35% height, scale \+ rotate entrance  
4. **Title**: 38px, weight 800, dark text, emphasize one word in blue  
5. **Subtitle**: 16px, 65% black, appears after title  
6. **Primary button**: Bottom \-24px, gradient, "התחל עכשיו"  
7. **Secondary button**: Below primary, blur white, "יש לי חשבון"  
8. **Animations**: 200ms cascade, 3.5s blob float, spring physics  
9. **Haptics**: Medium on primary, Light on secondary  
10. **Polish**: Bottom hint text, language toggle pill

---

This design system creates a cohesive, premium experience that feels both professional and approachable. Every screen should feel like it belongs to the same family while serving its unique purpose.

