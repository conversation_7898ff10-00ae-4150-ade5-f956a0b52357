{"name": "dlsa-backend", "version": "1.0.0", "description": "DLSA Driving Instructor App Backend", "main": "dist/server.js", "type": "module", "scripts": {"start": "node dist/server.js", "dev": "tsx watch src/server.ts", "build": "tsc", "test": "vitest", "lint": "eslint src --ext .ts", "format": "prettier --write src"}, "dependencies": {"@fastify/cors": "^9.0.1", "@fastify/helmet": "^11.1.1", "@fastify/rate-limit": "^9.1.0", "dotenv": "^17.2.1", "fastify": "^4.28.1", "mongodb": "^6.8.0", "pino": "^9.3.2", "pino-pretty": "^11.2.2", "zod": "^3.23.8"}, "devDependencies": {"@types/node": "^20.14.15", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "eslint": "^8.57.0", "prettier": "^3.3.3", "tsx": "^4.17.0", "typescript": "^5.5.4", "vitest": "^2.0.5"}, "engines": {"node": ">=20.0.0"}}