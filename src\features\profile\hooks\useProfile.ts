// src/features/profile/hooks/useProfile.ts
import { useState, useEffect } from 'react';
import { UserProfile } from '../types';
import { UserManager } from '@/services/api';

export const useProfile = () => {
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadProfile = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await UserManager.getUser();
      
      // Ensure data has required structure with defaults
      const profileData: UserProfile = {
        id: data?.id || 'temp-id',
        fullName: data?.fullName || 'משתמש',
        email: data?.email || '<EMAIL>',
        phone: data?.phone || '************',
        profileImage: data?.profileImage || null,
        memberSince: data?.memberSince || 'ינואר 2024',
        address: data?.address || {
          city: 'תל אביב',
          street: 'רוטשילד',
          buildingNumber: '1',
          apartment: '1',
        },
        instructor: data?.instructor || undefined,
        preferences: data?.preferences || {
          notifications: true,
          smsReminders: true,
          emailUpdates: false,
          autoSchedule: true,
          language: 'he',
          theme: 'light',
        },
        allowList: data?.allowList || [],
        stats: data?.stats || {
          lessonsCompleted: 0,
          hoursCompleted: 0,
          upcomingLessons: 0,
          averageRating: 0,
        },
      };
      
      setProfile(profileData);
    } catch (err) {
      setError('Failed to load profile');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const updateProfile = async (updates: Partial<UserProfile>) => {
    if (!profile) return;
    
    try {
      const updated = { ...profile, ...updates };
      await UserManager.updateUser(updated);
      setProfile(updated);
    } catch (err) {
      setError('Failed to update profile');
      throw err;
    }
  };

  useEffect(() => {
    loadProfile();
  }, []);

  return {
    profile,
    loading,
    error,
    refresh: loadProfile,
    update: updateProfile,
  };
};