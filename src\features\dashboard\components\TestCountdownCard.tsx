// src/features/dashboard/components/TestCountdownCard.tsx
import React from "react";
import {
  View,
  Text,
  Pressable,
  StyleSheet,
  Platform,
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { Ionicons, MaterialCommunityIcons } from "@expo/vector-icons";

interface Props {
  testDate: string;
  daysUntilTest: number;
  onViewTips: () => void;
}

export function TestCountdownCard({ testDate, daysUntilTest, onViewTips }: Props) {
  return (
    <View style={styles.card}>
      <LinearGradient
        colors={["#FEF3C7", "#FDE68A"]}
        style={styles.gradient}
      >
        <View style={styles.header}>
          <MaterialCommunityIcons name="card-account-details" size={32} color="#F59E0B" />
          <View style={styles.info}>
            <Text style={styles.title}>מבחן מעשי</Text>
            <Text style={styles.date}>{testDate}</Text>
          </View>
        </View>
        <View style={styles.countdown}>
          <Text style={styles.countdownNumber}>{daysUntilTest}</Text>
          <Text style={styles.countdownLabel}>ימים נותרו</Text>
        </View>
        <Pressable
          style={({ pressed }) => [
            styles.prepBtn,
            { opacity: pressed ? 0.8 : 1 },
          ]}
          onPress={onViewTips}
        >
          <Text style={styles.prepText}>טיפים למבחן</Text>
          <Ionicons name="arrow-forward" size={16} color="#92400E" />
        </Pressable>
      </LinearGradient>
    </View>
  );
}

const styles = StyleSheet.create({
  card: {
    backgroundColor: "#fff",
    borderRadius: 20,
    marginBottom: 16,
    overflow: "hidden",
    ...Platform.select({
      ios: {
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.08,
        shadowRadius: 8,
      },
      android: {
        elevation: 3,
      },
    }),
  },
  gradient: {
    padding: 20,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    gap: 16,
    marginBottom: 20,
  },
  info: {
    flex: 1,
  },
  title: {
    fontSize: 18,
    fontWeight: "700",
    color: "#92400E",
    marginBottom: 4,
  },
  date: {
    fontSize: 16,
    fontWeight: "600",
    color: "#B45309",
  },
  countdown: {
    flexDirection: "row",
    alignItems: "baseline",
    gap: 8,
    marginBottom: 16,
  },
  countdownNumber: {
    fontSize: 48,
    fontWeight: "800",
    color: "#92400E",
  },
  countdownLabel: {
    fontSize: 18,
    fontWeight: "600",
    color: "#B45309",
  },
  prepBtn: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    gap: 6,
    backgroundColor: "rgba(146, 64, 14, 0.1)",
    paddingVertical: 12,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: "rgba(146, 64, 14, 0.2)",
  },
  prepText: {
    fontSize: 14,
    fontWeight: "700",
    color: "#92400E",
  },
});