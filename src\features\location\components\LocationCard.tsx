import React, { useRef, useEffect } from 'react';
import {
  Pressable,
  View,
  Text,
  StyleSheet,
  Animated,
  Platform,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import { SavedLocation } from '@/src/core/types/location.types';
import { tokens } from '@/src/core/theme/tokens';

interface Props {
  location: SavedLocation;
  isSelected: boolean;
  onPress: () => void;
  index: number;
}

export const LocationCard: React.FC<Props> = ({
  location,
  isSelected,
  onPress,
  index,
}) => {
  const scaleAnim = useRef(new Animated.Value(0)).current;
  const selectedAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.spring(scaleAnim, {
      toValue: 1,
      delay: index * 50,
      tension: 50,
      friction: 8,
      useNativeDriver: true,
    }).start();
  }, []);

  useEffect(() => {
    Animated.spring(selectedAnim, {
      toValue: isSelected ? 1 : 0,
      tension: 30,
      friction: 8,
      useNativeDriver: true,
    }).start();
  }, [isSelected]);

  const handlePress = async () => {
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    
    Animated.sequence([
      Animated.timing(scaleAnim, {
        toValue: 0.95,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 30,
        friction: 5,
        useNativeDriver: true,
      }),
    ]).start();

    onPress();
  };

  const getIconForType = () => {
    const iconMap = {
      home: 'home',
      work: 'briefcase',
      current: 'location',
      favorite: 'star',
      custom: 'pin',
    };
    return iconMap[location.type] || 'pin';
  };

  const getColorsForType = () => {
    const colorMap = {
      home: ['#3B82F6', '#60A5FA'],
      work: ['#8B5CF6', '#A78BFA'],
      current: ['#10B981', '#34D399'],
      favorite: ['#F59E0B', '#FCD34D'],
      custom: ['#6B7280', '#9CA3AF'],
    };
    return colorMap[location.type] || ['#6B7280', '#9CA3AF'];
  };

  return (
    <Animated.View
      style={[
        styles.container,
        {
          transform: [
            { scale: scaleAnim },
            {
              scale: selectedAnim.interpolate({
                inputRange: [0, 1],
                outputRange: [1, 1.02],
              }),
            },
          ],
        },
      ]}
    >
      <Pressable onPress={handlePress} style={styles.pressable}>
        <Animated.View
          style={[
            styles.card,
            {
              borderWidth: selectedAnim.interpolate({
                inputRange: [0, 1],
                outputRange: [1.5, 2],
              }),
              borderColor: selectedAnim.interpolate({
                inputRange: [0, 1],
                outputRange: [tokens.colors.border, tokens.colors.primary],
              }),
            },
          ]}
        >
          <View style={styles.content}>
            <LinearGradient
              colors={getColorsForType()}
              style={styles.iconContainer}
            >
              <Ionicons
                name={getIconForType() as any}
                size={20}
                color="#FFFFFF"
              />
            </LinearGradient>
            
            <View style={styles.textContainer}>
              <Text style={styles.label}>{location.label}</Text>
              <Text style={styles.address}>{location.address}</Text>
            </View>
            
            {location.isDefault && (
              <View style={styles.defaultBadge}>
                <Text style={styles.defaultText}>ברירת מחדל</Text>
              </View>
            )}
            
            <Animated.View
              style={{
                opacity: selectedAnim,
                transform: [
                  {
                    scale: selectedAnim.interpolate({
                      inputRange: [0, 1],
                      outputRange: [0.5, 1],
                    }),
                  },
                ],
              }}
            >
              <Ionicons
                name="checkmark-circle"
                size={24}
                color={tokens.colors.primary}
              />
            </Animated.View>
          </View>
        </Animated.View>
      </Pressable>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 12,
  },
  pressable: {
    borderRadius: 16,
  },
  card: {
    backgroundColor: tokens.colors.backgroundPure,
    borderRadius: 16,
    padding: 16,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.05,
        shadowRadius: 4,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  textContainer: {
    flex: 1,
  },
  label: {
    fontSize: 16,
    fontWeight: '700',
    color: tokens.colors.textPrimary,
    marginBottom: 2,
  },
  address: {
    fontSize: 14,
    color: tokens.colors.textSecondary,
  },
  defaultBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
    backgroundColor: tokens.colors.warningBackground,
    marginRight: 8,
  },
  defaultText: {
    fontSize: 11,
    fontWeight: '600',
    color: tokens.colors.warning,
  },
});