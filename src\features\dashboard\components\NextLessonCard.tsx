// src/features/dashboard/components/NextLessonCard.tsx
import React, { useState } from "react";
import {
  View,
  Text,
  Pressable,
  StyleSheet,
  Platform,
  Alert,
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { Ionicons, MaterialCommunityIcons } from "@expo/vector-icons";
import * as Haptics from "expo-haptics";

interface Props {
  lesson: {
    id?: string;
    date: string;
    time: string;
    location: string;
    duration: string;
    type: string;
  };
  instructor: {
    name: string;
  };
  onReschedule: () => void;
  onNavigate: () => void;
  onCancelLesson: () => void;
  onLocationPress: () => void;
}

export function NextLessonCard({
  lesson,
  instructor,
  onReschedule,
  onNavigate,
  onCancelLesson,
  onLocationPress,
}: Props) {
  const handleCancel = () => {
    Alert.alert(
      "ביטול שיעור",
      "האם אתה בטוח שברצונך לבטל את השיעור?",
      [
        { text: "לא, השאר", style: "cancel" },
        { 
          text: "כן, בטל", 
          style: "destructive",
          onPress: async () => {
            await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
            onCancelLesson();
          }
        },
      ]
    );
  };

  return (
    <View style={styles.card}>
      <LinearGradient
        colors={["#EFF6FF", "#DBEAFE"]}
        style={styles.gradient}
      >
        {/* Urgent badge */}
        <View style={styles.urgentBadge}>
          <LinearGradient
            colors={["#FEE2E2", "#FCA5A5"]}
            style={styles.urgentBadgeGradient}
          >
            <Ionicons name="time" size={14} color="#DC2626" />
            <Text style={styles.urgentBadgeText}>היום!</Text>
          </LinearGradient>
        </View>

        <View style={styles.content}>
          {/* Main Lesson Info */}
          <View style={styles.heroSection}>
            <View style={styles.timeBlock}>
              <Text style={styles.timeLabel}>השיעור הבא שלך</Text>
              <Text style={styles.timeLarge}>{lesson.time}</Text>
              <Text style={styles.dateLarge}>{lesson.date}</Text>
            </View>
            
            <View style={styles.quickInfo}>
              <View style={styles.typePill}>
                <MaterialCommunityIcons name="steering" size={16} color="#3B82F6" />
                <Text style={styles.typeText}>{lesson.type}</Text>
              </View>
              <View style={styles.durationPill}>
                <Ionicons name="time-outline" size={16} color="#6B7280" />
                <Text style={styles.durationText}>{lesson.duration}</Text>
              </View>
            </View>
          </View>

          {/* Location */}
          <Pressable
            style={({ pressed }) => [
              styles.locationSection,
              { opacity: pressed ? 0.8 : 1 },
            ]}
            onPress={onLocationPress}
          >
            <View style={styles.locationIcon}>
              <Ionicons name="location" size={20} color="#3B82F6" />
            </View>
            <View style={styles.locationDetails}>
              <Text style={styles.locationLabel}>מיקום האיסוף</Text>
              <Text style={styles.locationText}>{lesson.location}</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color="#94A3B8" />
          </Pressable>

          {/* Instructor info bar */}
          <View style={styles.instructorBar}>
            <View style={styles.instructorAvatar}>
              <Text style={styles.instructorInitial}>
                {instructor.name.charAt(0)}
              </Text>
            </View>
            <View style={styles.instructorInfo}>
              <Text style={styles.instructorLabel}>המורה שלך</Text>
              <Text style={styles.instructorName}>{instructor.name}</Text>
            </View>
            <View style={styles.ratingContainer}>
              {[...Array(5)].map((_, i) => (
                <Ionicons
                  key={i}
                  name="star"
                  size={12}
                  color={i < 4 ? "#FCD34D" : "#E5E7EB"}
                />
              ))}
            </View>
          </View>

          {/* Actions - 3 buttons now */}
          <View style={styles.actions}>
            <Pressable
              style={({ pressed }) => [
                styles.actionBtn,
                styles.cancelBtn,
                { transform: [{ scale: pressed ? 0.95 : 1 }] },
              ]}
              onPress={handleCancel}
            >
              <Ionicons name="close-circle-outline" size={20} color="#EF4444" />
              <Text style={styles.cancelText}>ביטול</Text>
            </Pressable>

            <Pressable
              style={({ pressed }) => [
                styles.actionBtn,
                styles.rescheduleBtn,
                { transform: [{ scale: pressed ? 0.95 : 1 }] },
              ]}
              onPress={onReschedule}
            >
              <Ionicons name="calendar-outline" size={20} color="#3B82F6" />
              <Text style={styles.rescheduleText}>שינוי</Text>
            </Pressable>
          </View>
        </View>
      </LinearGradient>
    </View>
  );
}

const styles = StyleSheet.create({
  card: {
    backgroundColor: "#fff",
    borderRadius: 20,
    marginBottom: 16,
    overflow: "hidden",
    ...Platform.select({
      ios: {
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.08,
        shadowRadius: 8,
      },
      android: {
        elevation: 3,
      },
    }),
  },
  gradient: {
    padding: 20,
  },
  urgentBadge: {
    position: "absolute",
    top: 16,
    right: 16,
    zIndex: 10,
  },
  urgentBadgeGradient: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 12,
  },
  urgentBadgeText: {
    fontSize: 12,
    fontWeight: "700",
    color: "#DC2626",
  },
  content: {
    padding: 20,
  },
  heroSection: {
    marginBottom: 20,
  },
  timeBlock: {
    marginBottom: 16,
  },
  timeLabel: {
    fontSize: 14,
    fontWeight: "600",
    color: "#64748B",
    marginBottom: 4,
  },
  timeLarge: {
    fontSize: 42,
    fontWeight: "900",
    color: "#0F172A",
    letterSpacing: -1,
  },
  dateLarge: {
    fontSize: 18,
    fontWeight: "600",
    color: "#475569",
    marginTop: 2,
  },
  quickInfo: {
    flexDirection: "row",
    gap: 8,
  },
  typePill: {
    flexDirection: "row",
    alignItems: "center",
    gap: 6,
    backgroundColor: "#EFF6FF",
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 14,
    borderWidth: 1,
    borderColor: "#DBEAFE",
  },
  typeText: {
    fontSize: 14,
    fontWeight: "700",
    color: "#3B82F6",
  },
  durationPill: {
    flexDirection: "row",
    alignItems: "center",
    gap: 6,
    backgroundColor: "#F8FAFC",
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 14,
    borderWidth: 1,
    borderColor: "#E2E8F0",
  },
  durationText: {
    fontSize: 14,
    fontWeight: "600",
    color: "#64748B",
  },
  locationSection: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#F8FAFC",
    borderRadius: 16,
    padding: 14,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: "#E2E8F0",
  },
  locationIcon: {
    width: 36,
    height: 36,
    borderRadius: 10,
    backgroundColor: "#EFF6FF",
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  locationDetails: {
    flex: 1,
  },
  locationLabel: {
    fontSize: 12,
    fontWeight: "600",
    color: "#64748B",
    marginBottom: 2,
  },
  locationText: {
    fontSize: 14,
    fontWeight: "600",
    color: "#1E293B",
  },
  instructorBar: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#FAFBFC",
    borderRadius: 14,
    padding: 12,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: "#F1F5F9",
  },
  instructorAvatar: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: "#E0E7FF",
    justifyContent: "center",
    alignItems: "center",
    marginRight: 10,
  },
  instructorInitial: {
    fontSize: 16,
    fontWeight: "700",
    color: "#4338CA",
  },
  instructorInfo: {
    flex: 1,
  },
  instructorLabel: {
    fontSize: 11,
    fontWeight: "600",
    color: "#94A3B8",
    marginBottom: 1,
  },
  instructorName: {
    fontSize: 14,
    fontWeight: "700",
    color: "#1E293B",
  },
  ratingContainer: {
    flexDirection: "row",
    gap: 2,
  },
  actions: {
    flexDirection: "row",
    gap: 8,
  },
  actionBtn: {
    flex: 1,
    height: 48,
    borderRadius: 14,
    overflow: "hidden",
  },
  cancelBtn: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    gap: 4,
    backgroundColor: "#FEE2E2",
    borderWidth: 1.5,
    borderColor: "#FECACA",
  },
  cancelText: {
    fontSize: 14,
    fontWeight: "700",
    color: "#EF4444",
  },
  rescheduleBtn: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    gap: 4,
    backgroundColor: "#F1F5F9",
    borderWidth: 1.5,
    borderColor: "#CBD5E1",
  },
  rescheduleText: {
    fontSize: 14,
    fontWeight: "700",
    color: "#475569",
  },
  navigateBtn: {
    overflow: "hidden",
  },
  navigateBtnGradient: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    gap: 4,
    height: "100%",
    paddingHorizontal: 12,
  },
  navigateText: {
    fontSize: 14,
    fontWeight: "700",
    color: "#fff",
  },
});