import React, { useEffect, useRef } from "react";
import {
  View,
  Text,
  StyleSheet,
  Pressable,
  Animated,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Modal as RNModal,
  ViewStyle,
  Dimensions,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import * as Haptics from "expo-haptics";
import { tokens } from "@/src/core/theme/tokens";

type ModalSize = "small" | "medium" | "large" | "full";

export type ModalProps = {
  visible: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
  size?: ModalSize;
  /** Close when tapping the dark backdrop. Default: true */
  dismissOnBackdropPress?: boolean;
};

const { width: SCREEN_W, height: SCREEN_H } = Dimensions.get("window");

const sizeStyles: Record<ModalSize, ViewStyle> = {
  small:  { width: Math.min(SCREEN_W * 0.9, 520),  maxHeight: SCREEN_H * 0.40 },
  medium: { width: Math.min(SCREEN_W * 0.92, 640), maxHeight: SCREEN_H * 0.60 },
  large:  { width: Math.min(SCREEN_W * 0.94, 760), maxHeight: SCREEN_H * 0.80 },
  full:   { width: SCREEN_W, height: SCREEN_H, marginTop: 0, borderRadius: 0 },
};

export const Modal: React.FC<ModalProps> = ({
  visible,
  onClose,
  title,
  children,
  size = "medium",
  dismissOnBackdropPress = true,
}) => {
  const slideAnim = useRef(new Animated.Value(300)).current;
  const fadeAnim  = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (visible) {
      Animated.parallel([
        Animated.spring(slideAnim, { toValue: 0, tension: 65, friction: 10, useNativeDriver: true }),
        Animated.timing(fadeAnim,   { toValue: 1, duration: 200, useNativeDriver: true }),
      ]).start();
    } else {
      Animated.parallel([
        Animated.timing(slideAnim, { toValue: 300, duration: 200, useNativeDriver: true }),
        Animated.timing(fadeAnim,  { toValue: 0,   duration: 150, useNativeDriver: true }),
      ]).start();
    }
  }, [visible, slideAnim, fadeAnim]);

  const handleClose = async () => {
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    onClose();
  };

  return (
    <RNModal visible={visible} transparent animationType="none" onRequestClose={onClose}>
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.container}
      >
        {/* Backdrop */}
        <Animated.View style={[styles.backdrop, { opacity: fadeAnim }]}>
          {dismissOnBackdropPress && (
            <Pressable style={styles.backdropPress} onPress={handleClose} />
          )}
        </Animated.View>

        {/* Card */}
        <Animated.View
          style={[
            styles.modal,
            sizeStyles[size],
            { transform: [{ translateY: slideAnim }], opacity: fadeAnim },
          ]}
        >
          {/* Handle */}
          <View style={styles.handle} />

          {/* Header */}
          <View style={styles.header}>
            <Text style={styles.title}>{title}</Text>
            <Pressable onPress={handleClose} style={styles.closeButton}>
              <Ionicons name="close" size={24} color={tokens.colors.textSecondary} />
            </Pressable>
          </View>

          {/* Content */}
          <ScrollView
            style={styles.content}
            bounces={false}
            showsVerticalScrollIndicator={false}
          >
            {children}
          </ScrollView>
        </Animated.View>
      </KeyboardAvoidingView>
    </RNModal>
  );
};

export default Modal;

const styles = StyleSheet.create({
  container: { flex: 1, justifyContent: "flex-end" },
  backdrop: { ...StyleSheet.absoluteFillObject, backgroundColor: "rgba(0,0,0,0.35)" },
  backdropPress: { flex: 1 },
  modal: {
    alignSelf: "center",
    backgroundColor: tokens.colors.backgroundSurface,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    borderRadius: 24,
    paddingBottom: 12,
    paddingTop: 8,
    ...Platform.select({
      ios: {
        shadowColor: "#000",
        shadowOpacity: 0.1,
        shadowRadius: 20,
        shadowOffset: { width: 0, height: -2 },
      },
      default: { elevation: 20 },
    }),
  },
  handle: {
    alignSelf: "center",
    width: 36,
    height: 4,
    borderRadius: 2,
    marginBottom: 16,
    backgroundColor: tokens.colors.border,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 20,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: tokens.colors.border,
  },
  title: { fontSize: 18, fontWeight: "700", color: tokens.colors.textPrimary },
  closeButton: { padding: 6, borderRadius: 12 },
  content: { paddingHorizontal: 20, paddingVertical: 20 },
});
