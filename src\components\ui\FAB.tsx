import React, { useRef, useEffect } from 'react';
import {
  Pressable,
  StyleSheet,
  Animated,
  ViewStyle,
  Platform,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import { tokens } from '../../core/theme/tokens';

interface FABProps {
  icon: keyof typeof Ionicons.glyphMap;
  onPress: () => void;
  position?: 'bottom-right' | 'bottom-left' | 'bottom-center';
  size?: 'medium' | 'large';
  style?: ViewStyle;
}

export const FAB: React.FC<FABProps> = ({
  icon,
  onPress,
  position = 'bottom-right',
  size = 'large',
  style,
}) => {
  const scaleAnim = useRef(new Animated.Value(0)).current;
  const rotateAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.spring(scaleAnim, {
      toValue: 1,
      tension: 30,
      friction: 8,
      useNativeDriver: true,
    }).start();
  }, []);

  const handlePressIn = () => {
    Animated.parallel([
      Animated.spring(scaleAnim, {
        toValue: 0.9,
        useNativeDriver: true,
      }),
      Animated.timing(rotateAnim, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const handlePressOut = () => {
    Animated.parallel([
      Animated.spring(scaleAnim, {
        toValue: 1,
        useNativeDriver: true,
      }),
      Animated.timing(rotateAnim, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const handlePress = async () => {
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    onPress();
  };

  const rotation = rotateAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '90deg'],
  });

  return (
    <Animated.View style={[
      styles.container,
      positionStyles[position],
      {
        transform: [
          { scale: scaleAnim },
          { rotate: rotation },
        ],
      },
      style,
    ]}>
      <Pressable
        onPress={handlePress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        style={sizeStyles[size].button}
      >
        <LinearGradient
          colors={['#445EFB', '#6EA8FF']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={[styles.gradient, sizeStyles[size].gradient]}
        >
          <Ionicons
            name={icon}
            size={sizeStyles[size].iconSize}
            color={tokens.colors.textWhite}
          />
        </LinearGradient>
      </Pressable>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    zIndex: 999,
  },
  gradient: {
    justifyContent: 'center',
    alignItems: 'center',
    ...Platform.select({
      ios: tokens.shadows.primary,
      android: { 
        elevation: 8,
        shadowColor: '#445EFB',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.3,
        shadowRadius: 8,
      },
    }),
  },
});

const positionStyles = {
  'bottom-right': {
    bottom: 24,
    right: 24,
  },
  'bottom-left': {
    bottom: 24,
    left: 24,
  },
  'bottom-center': {
    bottom: 24,
    alignSelf: 'center' as const,
  },
};

const sizeStyles = {
  medium: {
    button: {
      width: 48,
      height: 48,
      borderRadius: 24,
      overflow: 'hidden' as const,
    },
    gradient: {
      width: 48,
      height: 48,
      borderRadius: 24,
    },
    iconSize: 24,
  },
  large: {
    button: {
      width: 56,
      height: 56,
      borderRadius: 28,
      overflow: 'hidden' as const,
    },
    gradient: {
      width: 56,
      height: 56,
      borderRadius: 28,
    },
    iconSize: 28,
  },
};