import React from 'react';
import { View, Text, StyleSheet, Pressable, ViewStyle } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';

interface ChipProps {
  label: string;
  selected?: boolean;
  onPress?: () => void;
  onDelete?: () => void;
  icon?: keyof typeof Ionicons.glyphMap;
  variant?: 'default' | 'outlined' | 'filled';
  size?: 'small' | 'medium' | 'large';
  style?: ViewStyle;
}

export const Chip: React.FC<ChipProps> = ({
  label,
  selected = false,
  onPress,
  onDelete,
  icon,
  variant = 'default',
  size = 'medium',
  style,
}) => {
  const handlePress = async () => {
    if (onPress) {
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      onPress();
    }
  };

  const handleDelete = async () => {
    if (onDelete) {
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      onDelete();
    }
  };

  const content = (
    <>
      {icon && (
        <Ionicons
          name={icon}
          size={sizeStyles[size].iconSize}
          color={selected ? '#FFFFFF' : '#6B7280'}
          style={styles.icon}
        />
      )}
      <Text style={[
        styles.label,
        sizeStyles[size].text,
        selected && styles.selectedLabel,
      ]}>
        {label}
      </Text>
      {onDelete && (
        <Pressable onPress={handleDelete} style={styles.deleteButton}>
          <Ionicons
            name="close-circle"
            size={sizeStyles[size].iconSize}
            color={selected ? '#FFFFFF' : '#6B7280'}
          />
        </Pressable>
      )}
    </>
  );

  if (onPress) {
    return (
      <Pressable
        onPress={handlePress}
        style={({ pressed }) => [
          styles.chip,
          sizeStyles[size].chip,
          variantStyles[variant],
          selected && styles.selected,
          pressed && styles.pressed,
          style,
        ]}
      >
        {content}
      </Pressable>
    );
  }

  return (
    <View style={[
      styles.chip,
      sizeStyles[size].chip,
      variantStyles[variant],
      selected && styles.selected,
      style,
    ]}>
      {content}
    </View>
  );
};

const styles = StyleSheet.create({
  chip: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'flex-start',
    borderRadius: 999,
  },
  icon: {
    marginRight: 4,
  },
  label: {
    fontWeight: '600',
    color: '#374151',
  },
  selectedLabel: {
    color: '#FFFFFF',
  },
  selected: {
    backgroundColor: '#445EFB',
    borderColor: '#445EFB',
  },
  pressed: {
    opacity: 0.8,
  },
  deleteButton: {
    marginLeft: 4,
  },
});

const sizeStyles = {
  small: {
    chip: { paddingHorizontal: 10, paddingVertical: 4 },
    text: { fontSize: 12 },
    iconSize: 14,
  },
  medium: {
    chip: { paddingHorizontal: 14, paddingVertical: 6 },
    text: { fontSize: 14 },
    iconSize: 16,
  },
  large: {
    chip: { paddingHorizontal: 18, paddingVertical: 8 },
    text: { fontSize: 16 },
    iconSize: 18,
  },
};

const variantStyles = StyleSheet.create({
  default: {
    backgroundColor: '#F3F4F6',
  },
  outlined: {
    backgroundColor: 'transparent',
    borderWidth: 1.5,
    borderColor: '#E5E7EB',
  },
  filled: {
    backgroundColor: '#E0E7FF',
  },
});