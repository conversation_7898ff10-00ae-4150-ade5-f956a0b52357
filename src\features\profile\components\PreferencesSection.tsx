import React from "react";
import { View, Text } from "react-native";
import { Card, Divider, Switch } from "@/src/components";
import { Ionicons, MaterialIcons } from "@expo/vector-icons";
import { tokens } from "@/src/core/theme/tokens";

type Prefs = { notifications: boolean; smsReminders: boolean; autoSchedule: boolean; };
type Props = {
  prefs: Prefs;
  onToggleNotifications: (v: boolean) => void;
  onToggleSms: (v: boolean) => void;
  onToggleAuto: (v: boolean) => void;
};

export function PreferencesSection({ prefs, onToggleNotifications, onToggleSms, onToggleAuto }: Props) {
  const Row = ({ icon, label, value, onChange }: any) => (
    <View style={{ flexDirection: "row", alignItems: "center", justifyContent: "space-between", paddingVertical: 6 }}>
      <View style={{ flexDirection: "row", alignItems: "center", gap: 8 }}>
        {icon}
        <Text style={{ fontSize: 16, color: tokens.colors.textPrimary }}>{label}</Text>
      </View>
      <Switch value={value} onValueChange={onChange} size="medium" />
    </View>
  );
  return (
    <Card variant="default" style={{ marginBottom: 16 }}>
      <Text style={{ fontSize: 18, fontWeight: "700", color: tokens.colors.textPrimary, marginBottom: 12 }}>העדפות</Text>
      <Row icon={<Ionicons name="notifications-outline" size={20} color={tokens.colors.textSecondary} />} label="התראות" value={prefs.notifications} onChange={onToggleNotifications} />
      <Divider spacing="small" />
      <Row icon={<Ionicons name="chatbubble-outline" size={20} color={tokens.colors.textSecondary} />} label="תזכורות SMS" value={prefs.smsReminders} onChange={onToggleSms} />
      <Divider spacing="small" />
      <Row icon={<MaterialIcons name="auto-awesome" size={20} color={tokens.colors.textSecondary} />} label="תזמון אוטומטי" value={prefs.autoSchedule} onChange={onToggleAuto} />
    </Card>
  );
}
