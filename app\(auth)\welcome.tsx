import { Stack, useRouter } from "expo-router";
import React, { useEffect, useRef } from "react";
import {
  Animated,
  Platform,
  StatusBar,
  StyleSheet,
  Text,
  View,
  Pressable,
  Dimensions,
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { BlurView } from "expo-blur";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import * as Haptics from "expo-haptics";
import Svg, { Path, Circle } from "react-native-svg";

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get("window");

export default function Welcome() {
  const insets = useSafeAreaInsets();
  const router = useRouter();

  // Animation values
  const logoScale = useRef(new Animated.Value(0.3)).current;
  const logoRotate = useRef(new Animated.Value(0)).current;
  const titleOpacity = useRef(new Animated.Value(0)).current;
  const titleTranslateY = useRef(new Animated.Value(30)).current;
  const subtitleOpacity = useRef(new Animated.Value(0)).current;
  const subtitleTranslateY = useRef(new Animated.Value(20)).current;
  const blob1Translate = useRef(new Animated.Value(0)).current;
  const blob2Translate = useRef(new Animated.Value(0)).current;
  const buttonTranslateY = useRef(new Animated.Value(100)).current;
  const buttonOpacity = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Entrance animations
    Animated.parallel([
      // Logo entrance with bounce
      Animated.spring(logoScale, {
        toValue: 1,
        tension: 20,
        friction: 7,
        delay: 200,
        useNativeDriver: true,
      }),
      // Logo subtle rotation
      Animated.timing(logoRotate, {
        toValue: 1,
        duration: 800,
        delay: 600,
        useNativeDriver: true,
      }),
      // Title animations
      Animated.parallel([
        Animated.timing(titleOpacity, {
          toValue: 1,
          duration: 800,
          delay: 400,
          useNativeDriver: true,
        }),
        Animated.spring(titleTranslateY, {
          toValue: 0,
          tension: 30,
          friction: 8,
          delay: 400,
          useNativeDriver: true,
        }),
      ]),
      // Subtitle animations
      Animated.parallel([
        Animated.timing(subtitleOpacity, {
          toValue: 1,
          duration: 800,
          delay: 600,
          useNativeDriver: true,
        }),
        Animated.spring(subtitleTranslateY, {
          toValue: 0,
          tension: 30,
          friction: 8,
          delay: 600,
          useNativeDriver: true,
        }),
      ]),
      // Button animations
      Animated.parallel([
        Animated.spring(buttonTranslateY, {
          toValue: 0,
          tension: 30,
          friction: 8,
          delay: 800,
          useNativeDriver: true,
        }),
        Animated.timing(buttonOpacity, {
          toValue: 1,
          duration: 600,
          delay: 800,
          useNativeDriver: true,
        }),
      ]),
    ]).start();

    // Floating blob animations
    const blob1Animation = Animated.loop(
      Animated.sequence([
        Animated.timing(blob1Translate, {
          toValue: 30,
          duration: 4000,
          useNativeDriver: true,
        }),
        Animated.timing(blob1Translate, {
          toValue: -30,
          duration: 4000,
          useNativeDriver: true,
        }),
      ])
    );

    const blob2Animation = Animated.loop(
      Animated.sequence([
        Animated.timing(blob2Translate, {
          toValue: -20,
          duration: 3500,
          useNativeDriver: true,
        }),
        Animated.timing(blob2Translate, {
          toValue: 20,
          duration: 3500,
          useNativeDriver: true,
        }),
      ])
    );

    blob1Animation.start();
    blob2Animation.start();

    return () => {
      blob1Animation.stop();
      blob2Animation.stop();
    };
  }, []);

  const handlePrimaryPress = async () => {
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    router.push("/(auth)/signup-step1");
  };

  const handleSecondaryPress = async () => {
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    router.push("/(auth)/signin");
  };

  return (
    <>
      <Stack.Screen options={{ headerShown: false }} />
      <StatusBar barStyle="dark-content" />
      <LinearGradient
        colors={["#EDF3FF", "#EAF1FF", "#E7EEFF"]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.container}
      >
        {/* Animated misty blobs */}
        <View style={styles.blobs} pointerEvents="none">
          <Animated.View 
            style={[
              styles.blob, 
              styles.blobA,
              {
                transform: [
                  { translateX: blob1Translate },
                  { translateY: Animated.multiply(blob1Translate, 0.5) }
                ]
              }
            ]} 
          />
          <Animated.View 
            style={[
              styles.blob, 
              styles.blobB,
              {
                transform: [
                  { translateX: blob2Translate },
                  { translateY: Animated.multiply(blob2Translate, -0.7) }
                ]
              }
            ]} 
          />
          <View style={[styles.blob, styles.blobC]} />
        </View>

        {/* Top right language pill */}
        <View style={[styles.topBar, { paddingTop: insets.top + 10 }]}>
          <BlurView intensity={30} tint="light" style={styles.langPill}>
            <Text style={styles.langIcon}>🌐</Text>
            <Text style={styles.langText}>עב</Text>
          </BlurView>
        </View>

        {/* Main content */}
        <View style={styles.content}>
          {/* Logo with animation */}
          <Animated.View 
            style={[
              styles.logoWrap,
              {
                transform: [
                  { scale: logoScale },
                  { 
                    rotate: logoRotate.interpolate({
                      inputRange: [0, 1],
                      outputRange: ['0deg', '5deg']
                    })
                  }
                ]
              }
            ]}
          >
            <View style={styles.logoShadow} />
            <View style={styles.logoTile}>
              <Svg width={28} height={28} viewBox="0 0 24 24">
                <Path
                  d="M4 16c4-5 6 5 10-5 1.5-3 3-3 6-3"
                  stroke="#4C6BFF"
                  strokeWidth={2.8}
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  fill="none"
                />
                <Circle cx="4" cy="16" r="2.5" fill="#4C6BFF" />
                <Circle cx="20" cy="8" r="2.5" fill="#6EA8FF" />
              </Svg>
            </View>
          </Animated.View>

          {/* Animated title */}
          <Animated.View
            style={{
              opacity: titleOpacity,
              transform: [{ translateY: titleTranslateY }]
            }}
          >
            <Text style={styles.kicker}>נהיגה חכמה יותר</Text>
            <Text style={styles.headline}>
              תמיד <Text style={styles.headlineEm}>בדרך</Text> הנכונה
            </Text>
          </Animated.View>

          {/* Animated subtitle */}
          <Animated.View
            style={{
              opacity: subtitleOpacity,
              transform: [{ translateY: subtitleTranslateY }]
            }}
          >
            <Text style={styles.sub}>
              תזמון שיעורים, מעקב והתקדמות — הכל במקום אחד
            </Text>
          </Animated.View>
        </View>

        {/* Bottom buttons */}
        <Animated.View 
          style={[
            styles.bottomSection,
            { 
              paddingBottom: insets.bottom + 24,
              opacity: buttonOpacity,
              transform: [{ translateY: buttonTranslateY }]
            }
          ]}
        >
          {/* Primary CTA */}
          <Pressable
            onPress={handlePrimaryPress}
            style={({ pressed }) => [
              styles.ctaWrap,
              { transform: [{ scale: pressed ? 0.97 : 1 }] },
            ]}
          >
            {({ pressed }) => (
              <LinearGradient
                colors={pressed ? ["#3A52E3", "#5E98F3"] : ["#445EFB", "#6EA8FF"]}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
                style={styles.ctaFill}
              >
                <Text style={styles.ctaText}>התחל עכשיו</Text>
                <View style={styles.ctaArrow}>
                  <Text style={styles.ctaArrowText}>→</Text>
                </View>
              </LinearGradient>
            )}
          </Pressable>

          {/* Secondary CTA */}
          <Pressable
            onPress={handleSecondaryPress}
            style={({ pressed }) => [
              { transform: [{ scale: pressed ? 0.98 : 1 }] },
            ]}
          >
            <BlurView intensity={24} tint="light" style={styles.secondary}>
              <View style={styles.secondaryBtn}>
                <Text style={styles.secondaryText}>יש לי כבר חשבון</Text>
              </View>
            </BlurView>
          </Pressable>

          {/* Bottom hint */}
          <Text style={styles.bottomHint}>
            נהיגה טובה מתחילה כאן
          </Text>
        </Animated.View>
      </LinearGradient>
    </>
  );
}

const styles = StyleSheet.create({
  container: { 
    flex: 1 
  },
  
  // Animated blobs
  blobs: { 
    position: "absolute", 
    width: "100%", 
    height: "100%" 
  },
  blob: {
    position: "absolute",
    borderRadius: 999,
    backgroundColor: "#ffffff",
    ...Platform.select({
      ios: { 
        shadowColor: "#4C6BFF", 
        shadowOpacity: 0.15, 
        shadowRadius: 60,
        shadowOffset: { width: 0, height: 20 }
      },
      android: { elevation: 0 },
    }),
  },
  blobA: { 
    top: -80, 
    left: -60,
    width: 260,
    height: 260,
    opacity: 0.45,
  },
  blobB: { 
    bottom: 100, 
    right: -80, 
    opacity: 0.35,
    width: 280,
    height: 280,
  },
  blobC: { 
    top: SCREEN_HEIGHT * 0.35, 
    left: -120, 
    opacity: 0.25, 
    width: 320, 
    height: 320,
  },

  // Top bar
  topBar: {
    width: "100%",
    paddingHorizontal: 22,
    alignItems: "flex-end",
  },
  langPill: {
    flexDirection: "row",
    alignItems: "center",
    gap: 6,
    paddingHorizontal: 14,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: "rgba(255,255,255,0.75)",
    borderWidth: StyleSheet.hairlineWidth,
    borderColor: "rgba(0,0,0,0.08)",
  },
  langIcon: { 
    fontSize: 15 
  },
  langText: { 
    fontSize: 13, 
    fontWeight: "700", 
    color: "#4C6BFF", 
    letterSpacing: 0.4 
  },

  // Main content
  content: {
    flex: 1,
    alignItems: "center",
    paddingHorizontal: 28,
    justifyContent: "center",
    marginTop: -40,
  },

  // Logo
  logoWrap: { 
    marginBottom: 32,
  },
  logoShadow: {
    position: "absolute",
    width: 80,
    height: 80,
    borderRadius: 20,
    backgroundColor: "#4C6BFF",
    opacity: 0.15,
    top: 6,
    alignSelf: "center",
    ...Platform.select({
      ios: {
        shadowColor: "#4C6BFF",
        shadowOpacity: 0.3,
        shadowRadius: 30,
        shadowOffset: { width: 0, height: 15 }
      },
      android: { elevation: 0 },
    }),
  },
  logoTile: {
    width: 72,
    height: 72,
    borderRadius: 20,
    backgroundColor: "#ffffff",
    alignItems: "center",
    justifyContent: "center",
    ...Platform.select({
      ios: {
        shadowColor: "#000",
        shadowOpacity: 0.12,
        shadowRadius: 16,
        shadowOffset: { width: 0, height: 8 },
      },
      android: { elevation: 8 },
    }),
  },

  // Typography
  kicker: {
    color: "#5873FF",
    fontSize: 14,
    fontWeight: "800",
    letterSpacing: 1.2,
    textAlign: "center",
    marginBottom: 12,
  },
  headline: {
    fontSize: 38,
    fontWeight: "800",
    color: "#0B0B0B",
    textAlign: "center",
    lineHeight: 46,
  },
  headlineEm: { 
    fontWeight: "900",
    color: "#445EFB",
  },
  sub: {
    fontSize: 16,
    color: "rgba(0,0,0,0.65)",
    marginTop: 12,
    textAlign: "center",
    lineHeight: 24,
    paddingHorizontal: 20,
  },

  // Bottom section
  bottomSection: {
    paddingHorizontal: 22,
  },

  // Primary button
  ctaWrap: {
    width: "100%",
    borderRadius: 16,
    overflow: "hidden",
    marginBottom: 12,
    ...Platform.select({
      ios: {
        shadowColor: "#445EFB",
        shadowOpacity: 0.3,
        shadowRadius: 20,
        shadowOffset: { width: 0, height: 10 },
      },
      android: { elevation: 8 },
    }),
  },
  ctaFill: { 
    paddingVertical: 18, 
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "center",
    gap: 8,
  },
  ctaText: {
    color: "#FFFFFF",
    fontSize: 17,
    fontWeight: "800",
    letterSpacing: 0.3,
  },
  ctaArrow: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: "rgba(255,255,255,0.25)",
    alignItems: "center",
    justifyContent: "center",
  },
  ctaArrowText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "700",
  },

  // Secondary button
  secondary: {
    width: "100%",
    borderRadius: 16,
    backgroundColor: "rgba(255,255,255,0.82)",
    borderWidth: 1,
    borderColor: "rgba(0,0,0,0.06)",
    ...Platform.select({
      ios: {
        shadowColor: "#000",
        shadowOpacity: 0.06,
        shadowRadius: 10,
        shadowOffset: { width: 0, height: 4 },
      },
      android: { elevation: 2 },
    }),
  },
  secondaryBtn: { 
    paddingVertical: 18, 
    alignItems: "center" 
  },
  secondaryText: {
    fontSize: 16,
    fontWeight: "700",
    color: "rgba(0,0,0,0.75)",
  },

  // Bottom hint
  bottomHint: {
    textAlign: "center",
    fontSize: 12,
    color: "rgba(0,0,0,0.4)",
    marginTop: 20,
    fontWeight: "600",
    letterSpacing: 0.3,
  },
});