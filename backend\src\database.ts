import { MongoClient, Db, Collection } from "mongodb";
import type { User } from "./types.js";

/**
 * Database connection and setup
 */

let client: MongoClient;
let db: Db;
let usersCollection: Collection<User>;

export async function connectToDatabase(): Promise<void> {
  const mongoUri = process.env.MONGODB_URI;
  const dbName = process.env.MONGODB_DB || "drivio";

  if (!mongoUri) {
    throw new Error("MONGODB_URI environment variable is required");
  }

  console.log("Connecting to MongoDB...");

  client = new MongoClient(mongoUri, {
    serverSelectionTimeoutMS: 5000,
    connectTimeoutMS: 10000,
  });

  try {
    await client.connect();
    db = client.db(dbName);
    usersCollection = db.collection<User>("users");

    // Create indexes
    await createIndexes();

    console.log(`Connected to MongoDB database: ${dbName}`);
  } catch (error) {
    console.error("Failed to connect to MongoDB:", error);
    throw error;
  }
}

async function createIndexes(): Promise<void> {
  try {
    // Unique index on phone number
    await usersCollection.createIndex(
      { phone: 1 },
      { unique: true, name: "phone_unique" },
    );

    // Index on createdAt for sorting
    await usersCollection.createIndex(
      { createdAt: -1 },
      { name: "createdAt_desc" },
    );

    // Text index for search functionality
    await usersCollection.createIndex(
      {
        fullName: "text",
        email: "text",
        "address.city": "text",
      },
      { name: "search_text" },
    );

    console.log("Database indexes created successfully");
  } catch (error) {
    console.error("Failed to create indexes:", error);
    // Don't throw - indexes might already exist
  }
}

export function getUsersCollection(): Collection<User> {
  if (!usersCollection) {
    throw new Error("Database not connected. Call connectToDatabase() first.");
  }
  return usersCollection;
}

export function getDatabase(): Db {
  if (!db) {
    throw new Error("Database not connected. Call connectToDatabase() first.");
  }
  return db;
}

export async function closeDatabaseConnection(): Promise<void> {
  if (client) {
    await client.close();
    console.log("MongoDB connection closed");
  }
}

// Health check function
export async function isDatabaseHealthy(): Promise<boolean> {
  try {
    if (!db) return false;
    await db.admin().ping();
    return true;
  } catch {
    return false;
  }
}
