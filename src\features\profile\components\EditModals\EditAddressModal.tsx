import React, { useState, useEffect } from "react";
import { Modal as RNModal, View, Text, TextInput, Pressable, StyleSheet } from "react-native";
import { tokens } from "@/src/core/theme/tokens";

type Address = { city?: string; street?: string; buildingNumber?: string; apartment?: string };
type Props = {
  visible: boolean;
  currentAddress: Address;
  onClose: () => void;
  onSave: (address: Address) => void;
};

export function EditAddressModal({ visible, currentAddress, onClose, onSave }: Props) {
  const [addr, setAddr] = useState<Address>(currentAddress || {});
  useEffect(() => setAddr(currentAddress || {}), [currentAddress, visible]);

  return (
    <RNModal visible={visible} transparent animationType="fade" onRequestClose={onClose}>
      <View style={styles.backdrop}>
        <View style={styles.card}>
          <Text style={styles.title}>עריכת כתובת</Text>
          <TextInput placeholder="עיר" value={addr.city} onChangeText={(t)=>setAddr(a=>({...a, city:t}))} style={styles.input} placeholderTextColor={tokens.colors.textTertiary}/>
          <TextInput placeholder="רחוב" value={addr.street} onChangeText={(t)=>setAddr(a=>({...a, street:t}))} style={styles.input} placeholderTextColor={tokens.colors.textTertiary}/>
          <TextInput placeholder="מס' בית" value={addr.buildingNumber} onChangeText={(t)=>setAddr(a=>({...a, buildingNumber:t}))} style={styles.input} placeholderTextColor={tokens.colors.textTertiary}/>
          <TextInput placeholder="דירה" value={addr.apartment} onChangeText={(t)=>setAddr(a=>({...a, apartment:t}))} style={styles.input} placeholderTextColor={tokens.colors.textTertiary}/>
          <View style={styles.row}>
            <Pressable onPress={onClose} style={styles.btnGhost}><Text style={styles.btnGhostText}>ביטול</Text></Pressable>
            <Pressable onPress={() => { onSave(addr); onClose(); }} style={styles.btn}>
              <Text style={styles.btnText}>שמור</Text>
            </Pressable>
          </View>
        </View>
      </View>
    </RNModal>
  );
}

const styles = StyleSheet.create({
  backdrop: { flex: 1, backgroundColor: "rgba(0,0,0,0.35)", justifyContent: "center", padding: 20 },
  card: { backgroundColor: tokens.colors.backgroundSurface, borderRadius: 16, padding: 16 },
  title: { fontSize: 18, fontWeight: "700", color: tokens.colors.textPrimary, marginBottom: 12, textAlign: "center" },
  input: { borderWidth: 1, borderColor: tokens.colors.border, borderRadius: 12, paddingHorizontal: 12, height: 44, color: tokens.colors.textPrimary, marginBottom: 10 },
  row: { flexDirection: "row", justifyContent: "flex-end", gap: 12, marginTop: 6 },
  btn: { backgroundColor: tokens.colors.primary, paddingHorizontal: 16, height: 40, borderRadius: 10, justifyContent: "center" },
  btnText: { color: "#fff", fontWeight: "700" },
  btnGhost: { paddingHorizontal: 8, height: 40, justifyContent: "center" },
  btnGhostText: { color: tokens.colors.primary, fontWeight: "700" },
});
