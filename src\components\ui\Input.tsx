// src/components/ui/Input.tsx
import React, { useState, useRef } from 'react';
import {
  View,
  TextInput,
  Text,
  StyleSheet,
  Animated,
  Pressable,
  TextInputProps,
  ViewStyle,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import { tokens } from '../../core/theme/tokens';
import { NativeSyntheticEvent, TextInputFocusEventData } from "react-native";

interface InputProps extends TextInputProps {
  label?: string;
  error?: string;
  hint?: string;
  icon?: keyof typeof Ionicons.glyphMap;
  rightIcon?: keyof typeof Ionicons.glyphMap;
  onRightIconPress?: () => void;
  containerStyle?: ViewStyle;
  variant?: 'default' | 'filled' | 'minimal';
  size?: 'small' | 'medium' | 'large';
  floatingLabel?: boolean;
}

export const Input: React.FC<InputProps> = ({
  label,
  error,
  hint,
  icon,
  rightIcon,
  onRightIconPress,
  containerStyle,
  variant = 'default',
  size = 'medium',
  floatingLabel = false,
  value,
  onFocus,
  onBlur,
  ...props
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const borderAnim = useRef(new Animated.Value(0)).current;
  const labelAnim = useRef(new Animated.Value(value ? 1 : 0)).current;

const isEmpty = (v: unknown) =>
  v == null || (typeof v === "string" && v.length === 0);

const handleFocus = (e: NativeSyntheticEvent<TextInputFocusEventData>) => {
  setIsFocused(true);

  const animations: Animated.CompositeAnimation[] = [
    Animated.timing(borderAnim, {
      toValue: 1,
      duration: 200,
      useNativeDriver: false,
    }),
  ];

  if (floatingLabel) {
    animations.push(
      Animated.timing(labelAnim, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
      })
    );
  }

  Animated.parallel(animations).start();
  onFocus?.(e);
};

const handleBlur = (e: NativeSyntheticEvent<TextInputFocusEventData>) => {
  setIsFocused(false);

  const animations: Animated.CompositeAnimation[] = [
    Animated.timing(borderAnim, {
      toValue: 0,
      duration: 200,
      useNativeDriver: false,
    }),
  ];

  if (floatingLabel && isEmpty(value)) {
    animations.push(
      Animated.timing(labelAnim, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      })
    );
  }

  Animated.parallel(animations).start();
  onBlur?.(e);
};

  const borderColor = borderAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [
      error ? tokens.colors.error : tokens.colors.border,
      error ? tokens.colors.error : tokens.colors.primary,
    ],
  });

  const borderWidth = borderAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [1, 2],
  });

  return (
    <View style={[styles.container, containerStyle]}>
      {label && !floatingLabel && (
        <Text style={styles.label}>{label}</Text>
      )}
      
      <Animated.View style={[
        styles.inputWrapper,
        sizeStyles[size],
        variantStyles[variant],
        { borderColor, borderWidth },
      ]}>
        {floatingLabel && (
          <Animated.Text style={[
            styles.floatingLabel,
            {
              transform: [
                {
                  translateY: labelAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: [0, -28],
                  }),
                },
                {
                  scale: labelAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: [1, 0.85],
                  }),
                },
              ],
            },
          ]}>
            {label}
          </Animated.Text>
        )}
        
        {icon && (
          <Animated.View style={[
            styles.leftIcon,
            {
              transform: [{
                scale: borderAnim.interpolate({
                  inputRange: [0, 1],
                  outputRange: [1, 1.1],
                }),
              }],
            },
          ]}>
            <Ionicons 
              name={icon} 
              size={20} 
              color={isFocused ? tokens.colors.primary : tokens.colors.textTertiary} 
            />
          </Animated.View>
        )}
        
        <TextInput
          style={[
            styles.input,
            sizeStyles[size].text,
            icon && styles.inputWithIcon,
            rightIcon && styles.inputWithRightIcon,
          ]}
          placeholderTextColor={tokens.colors.textTertiary}
          onFocus={handleFocus}
          onBlur={handleBlur}
          value={value}
          {...props}
        />
        
        {rightIcon && (
          <Pressable 
            onPress={onRightIconPress} 
            style={styles.rightIcon}
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
          >
            <Ionicons name={rightIcon} size={20} color={tokens.colors.textTertiary} />
          </Pressable>
        )}
      </Animated.View>
      
      {(error || hint) && (
        <Animated.Text style={[
          error ? styles.error : styles.hint,
          {
            opacity: borderAnim.interpolate({
              inputRange: [0, 1],
              outputRange: [0.7, 1],
            }),
          },
        ]}>
          {error || hint}
        </Animated.Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 20,
  },
  label: {
    fontSize: 14,
    fontWeight: '600',
    color: tokens.colors.textPrimary,
    marginBottom: 8,
  },
  floatingLabel: {
    position: 'absolute',
    left: 16,
    fontSize: 16,
    color: tokens.colors.textTertiary,
    backgroundColor: tokens.colors.backgroundPure,
    paddingHorizontal: 4,
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 12,
    backgroundColor: tokens.colors.backgroundPure,
    overflow: 'hidden',
  },
  input: {
    flex: 1,
    color: tokens.colors.textPrimary,
    paddingHorizontal: 16,
  },
  inputWithIcon: {
    paddingLeft: 0,
  },
  inputWithRightIcon: {
    paddingRight: 0,
  },
  leftIcon: {
    marginLeft: 16,
    marginRight: 8,
  },
  rightIcon: {
    padding: 12,
  },
  error: {
    fontSize: 12,
    color: tokens.colors.error,
    marginTop: 6,
    marginLeft: 4,
  },
  hint: {
    fontSize: 12,
    color: tokens.colors.textTertiary,
    marginTop: 6,
    marginLeft: 4,
  },
});

const sizeStyles = {
  small: { 
    height: 44,
    text: { fontSize: 14 },
  },
  medium: { 
    height: 52,
    text: { fontSize: 16 },
  },
  large: { 
    height: 60,
    text: { fontSize: 18 },
  },
};

const variantStyles = StyleSheet.create({
  default: {
    borderWidth: 1,
  },
  filled: {
    backgroundColor: tokens.colors.backgroundSurface,
    borderWidth: 0,
  },
  minimal: {
    borderWidth: 0,
    borderBottomWidth: 1,
    borderRadius: 0,
  },
});