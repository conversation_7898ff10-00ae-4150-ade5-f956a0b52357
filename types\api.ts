// Shared types for the frontend app matching backend API contract

export interface Address {
  city: string;
  street: string;
  buildingNumber: string;
  apartment?: string;
}

export interface FormData {
  fullName: string;
  phone: string;
  email?: string;
}

export interface AddressData {
  city: string;
  street: string;
  buildingNumber: string;
  apartment: string;
}

export interface User {
  _id: string;
  fullName: string;
  phone: string;
  email?: string;
  address?: Address;
  status: 'PENDING' | 'COMPLETE';
  regStage: 0 | 1 | 2;
  deviceId: string;
  createdAt: Date;
  updatedAt: Date;
}

// API Response types
export interface APIResponse<T = any> {
  data?: T;
  error?: string;
  message?: string;
}

// Error types
export interface APIError {
  status: number;
  message: string;
  code?: string;
  details?: any;
}

// UI State types
export interface LoadingState {
  isLoading: boolean;
  error?: string | null;
}

export interface RegistrationState extends LoadingState {
  step: 1 | 2;
  regId?: string;
  formData?: FormData;
  addressData?: Partial<AddressData>;
}