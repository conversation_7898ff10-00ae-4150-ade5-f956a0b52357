import React from 'react';
import { View, Text, StyleSheet, ViewStyle } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { tokens } from '../../core/theme/tokens';

interface BadgeProps {
  children: React.ReactNode;
  variant?: 'default' | 'primary' | 'success' | 'warning' | 'error' | 'gradient';
  size?: 'small' | 'medium' | 'large';
  icon?: React.ReactNode;
  style?: ViewStyle;
}

export const Badge: React.FC<BadgeProps> = ({
  children,
  variant = 'default',
  size = 'medium',
  icon,
  style,
}) => {
  const content = (
    <>
      {icon && <View style={styles.icon}>{icon}</View>}
      <Text style={[styles.text, sizeStyles[size].text, variantStyles[variant].text]}>
        {children}
      </Text>
    </>
  );

  if (variant === 'gradient') {
    return (
      <LinearGradient
        colors={['#445EFB', '#6EA8FF']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={[styles.badge, sizeStyles[size].badge, style]}
      >
        {content}
      </LinearGradient>
    );
  }

  return (
    <View style={[
      styles.badge,
      sizeStyles[size].badge,
      variantStyles[variant].badge,
      style,
    ]}>
      {content}
    </View>
  );
};

const styles = StyleSheet.create({
  badge: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'flex-start',
    borderRadius: 12,
  },
  text: {
    fontWeight: '700',
  },
  icon: {
    marginRight: 4,
  },
});

const sizeStyles = {
  small: {
    badge: { paddingHorizontal: 8, paddingVertical: 4 },
    text: { fontSize: 11 },
  },
  medium: {
    badge: { paddingHorizontal: 12, paddingVertical: 6 },
    text: { fontSize: 12 },
  },
  large: {
    badge: { paddingHorizontal: 16, paddingVertical: 8 },
    text: { fontSize: 14 },
  },
};

const variantStyles = {
  default: {
    badge: { backgroundColor: '#F3F4F6' },
    text: { color: tokens.colors.textSecondary },
  },
  primary: {
    badge: { backgroundColor: tokens.colors.infoBackground },
    text: { color: tokens.colors.info },
  },
  success: {
    badge: { backgroundColor: tokens.colors.successBackground },
    text: { color: tokens.colors.success },
  },
  warning: {
    badge: { backgroundColor: tokens.colors.warningBackground },
    text: { color: tokens.colors.warning },
  },
  error: {
    badge: { backgroundColor: tokens.colors.errorBackground },
    text: { color: tokens.colors.error },
  },
  gradient: {
    badge: {},
    text: { color: tokens.colors.textWhite },
  },
};