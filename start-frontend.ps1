# 1) Go to the app folder
cd "C:\Users\<USER>\Desktop\DLSA-app-Vscode-version"

# 2) (Optional) point the app to your local backend on port 3000
$ip = (Get-NetIPAddress -AddressFamily IPv4 | Where-Object {
  $_.IPAddress -notlike '127.*' -and $_.InterfaceAlias -notmatch 'vEthernet|Loopback|Virtual'
} | Select-Object -First 1 -ExpandProperty IPAddress)
$env:EXPO_PUBLIC_API_BASE = "http://$ip:3000"

# 3) Start Expo and show the QR (LAN)
npx expo start --lan
