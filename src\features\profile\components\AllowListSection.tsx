import React from "react";
import { View, Text, Pressable } from "react-native";
import { Card, Divider, ListItem, Avatar, IconButton, Button } from "@/src/components";
import { tokens } from "@/src/core/theme/tokens";

type Person = { id: number; name: string; phone: string };
type Props = {
  list: Person[];
  onManage: () => void;
  onRemove: (id: number) => void;
  onAdd: () => void;
};

export function AllowListSection({ list, onManage, onRemove, onAdd }: Props) {
  return (
    <Card variant="default" style={{ marginBottom: 16 }}>
      <View style={{ flexDirection: "row", justifyContent: "space-between", alignItems: "center", marginBottom: 12 }}>
        <Text style={{ fontSize: 18, fontWeight: "700", color: tokens.colors.textPrimary }}>רשימת מורשים</Text>
        <Pressable onPress={onManage}><Text style={{ fontSize: 14, fontWeight: "600", color: tokens.colors.primary }}>נהל</Text></Pressable>
      </View>
      <Text style={{ fontSize: 14, color: tokens.colors.textTertiary, marginBottom: 16 }}>אנשים שיכולים לקבל עדכונים על השיעורים שלך</Text>
      {list.map((p, i) => (
        <React.Fragment key={p.id}>
          {i > 0 && <Divider spacing="small" />}
          <ListItem
            title={p.name}
            subtitle={p.phone}
            leftElement={<Avatar name={p.name} size="small" />}
            rightElement={<IconButton icon="close-circle" size="small" variant="ghost" onPress={() => onRemove(p.id)} />}
          />
        </React.Fragment>
      ))}
      <Button variant="ghost" size="small" icon="person-add" style={{ marginTop: 12 }} onPress={onAdd}>
        הוסף איש קשר
      </Button>
    </Card>
  );
}
