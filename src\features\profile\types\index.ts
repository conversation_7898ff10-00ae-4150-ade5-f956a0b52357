// src/features/profile/types/index.ts
export interface UserProfile {
  id: string;
  fullName: string;
  email: string;
  phone: string;
  profileImage?: string | null;
  memberSince: string;
  address: Address;
  instructor?: Instructor;
  preferences: UserPreferences;
  allowList: AllowedContact[];
  stats: UserStats;
}

export interface Address {
  city: string;
  street: string;
  buildingNumber: string;
  apartment?: string;
}

export interface Instructor {
  id: string;
  name: string;
  phone: string;
  email?: string;
  profileImage?: string;
  rating: number;
  connected: boolean;
}

export interface UserPreferences {
  notifications: boolean;
  smsReminders: boolean;
  emailUpdates: boolean;
  autoSchedule: boolean;
  language: 'he' | 'en' | 'ar';
  theme: 'light' | 'dark' | 'auto';
}

export interface AllowedContact {
  id: string;
  name: string;
  phone: string;
  relationship?: string;
}

export interface UserStats {
  lessonsCompleted: number;
  hoursCompleted: number;
  upcomingLessons: number;
  averageRating: number;
  totalDistance?: number;
  successRate?: number;
}