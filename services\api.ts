import * as SecureStore from 'expo-secure-store';
import * as Crypto from 'expo-crypto';

// Configuration
const API_BASE_URL = __DEV__ ? 'http://10.100.102.88:3000' : 'https://your-production-api.com';
const DEV_ADMIN_TOKEN = 'dev-only-secret-12345'; // Should match backend .env
const DEV_ADMIN_PHONE = '**********'; // Admin user for development bypass

// Connection test function
export const ConnectionTest = {
  async testConnection(): Promise<boolean> {
    console.log('Testing connection to:', API_BASE_URL);
    try {
      const response = await fetch(`${API_BASE_URL}/healthz`, {
        method: 'GET',
        timeout: 5000,
        headers: {
          'Content-Type': 'application/json',
        },
      });
      console.log('Connection test response status:', response.status);
      return response.ok;
    } catch (error) {
      console.error('Connection test failed:', error);
      return false;
    }
  }
};

// Types based on backend API contract
export interface RegistrationStartRequest {
  fullName: string;
  phone: string;
  email?: string;
  deviceId: string;
}

export interface RegistrationCompleteRequest {
  address: {
    city: string;
    street: string;
    buildingNumber: string;
    apartment?: string;
  };
}

export interface RegistrationStartResponse {
  regId: string;
  status: 'PENDING';
}

export interface RegistrationCompleteResponse {
  status: 'COMPLETE';
  userId: string;
  createdAt: string;
}

export interface UserListItem {
  _id: string;
  fullName: string;
  phone: string;
  email?: string;
  city?: string;
  createdAt: string;
}

export interface UserListResponse {
  items: UserListItem[];
  nextCursor?: string | null;
}

// API Error types
export class APIError extends Error {
  constructor(
    public status: number,
    message: string,
    public code?: string,
    public details?: any
  ) {
    super(message);
    this.name = 'APIError';
  }
}

// Device ID management
export const DeviceManager = {
  async getOrCreateDeviceId(): Promise<string> {
    const stored = await SecureStore.getItemAsync('deviceId');
    if (stored) return stored;
    
    const newDeviceId = Crypto.randomUUID();
    await SecureStore.setItemAsync('deviceId', newDeviceId);
    return newDeviceId;
  },

  async getDeviceId(): Promise<string | null> {
    return await SecureStore.getItemAsync('deviceId');
  }
};

// Registration ID management
export const RegistrationManager = {
  async saveRegId(regId: string): Promise<void> {
    await SecureStore.setItemAsync('regId', regId);
  },

  async getRegId(): Promise<string | null> {
    return await SecureStore.getItemAsync('regId');
  },

  async clearRegId(): Promise<void> {
    await SecureStore.deleteItemAsync('regId');
  }
};

// User session management
export const UserManager = {
  async saveUser(user: any): Promise<void> {
    await SecureStore.setItemAsync('currentUser', JSON.stringify(user));
  },

  async getUser(): Promise<any | null> {
    const userData = await SecureStore.getItemAsync('currentUser');
    return userData ? JSON.parse(userData) : null;
  },

  async clearUser(): Promise<void> {
    await SecureStore.deleteItemAsync('currentUser');
  }
};

// HTTP helper with error handling
async function apiRequest<T>(
  endpoint: string,
  options: RequestInit = {},
  timeout = 15000
): Promise<T> {
  const url = `${API_BASE_URL}${endpoint}`;
  console.log(`API Request: ${options.method || 'GET'} ${url}`);
  
  // Add timeout
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeout);
  
  try {
    const response = await fetch(url, {
      ...options,
      signal: controller.signal,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
    });

    clearTimeout(timeoutId);
    console.log(`API Response: ${response.status} ${response.statusText}`);

    if (!response.ok) {
      let errorMessage = `HTTP ${response.status}`;
      let errorCode: string | undefined;
      let errorDetails: any;
      
      try {
        const errorData = await response.json();
        errorMessage = errorData.error || errorMessage;
        errorCode = errorData.code;
        errorDetails = errorData.details;
        console.error('API Error Details:', errorData);
      } catch {
        // If JSON parsing fails, use status text
        errorMessage = response.statusText || errorMessage;
        console.error('API Error (no JSON):', response.status, response.statusText);
      }
      
      throw new APIError(response.status, errorMessage, errorCode, errorDetails);
    }

    const responseData = await response.json();
    console.log('API Success:', responseData);
    return responseData;
  } catch (error) {
    clearTimeout(timeoutId);
    
    if (error.name === 'AbortError') {
      console.error('API Request Timeout:', url);
      throw new APIError(408, 'Request timeout - server took too long to respond');
    }
    
    if (error instanceof APIError) {
      throw error;
    }
    
    // Network or other errors
    console.error('Network Error:', error);
    const networkError = error as any;
    
    // More specific network error messages
    if (networkError.code === 'NETWORK_REQUEST_FAILED') {
      throw new APIError(0, 'Cannot connect to server. Please check your internet connection and ensure the server is running.');
    }
    
    if (networkError.message?.includes('fetch')) {
      throw new APIError(0, 'Network connection failed. Make sure you are connected to the same WiFi network as the server.');
    }
    
    throw new APIError(0, 'Network error or server unavailable. Please check your connection.');
  }
}

// API methods
export const AuthAPI = {
  async loginWithPhone(phone: string): Promise<{ user: any; token?: string }> {
    console.log('Attempting login with phone:', phone.replace(/\d/g, '*'));
    
    // Format phone number to include +972 if not present
    let formattedPhone = phone.replace(/[^0-9]/g, '');
    if (formattedPhone.startsWith('0')) {
      formattedPhone = '+972' + formattedPhone.slice(1);
    } else if (!formattedPhone.startsWith('972')) {
      formattedPhone = '+972' + formattedPhone;
    } else {
      formattedPhone = '+' + formattedPhone;
    }
    console.log('Formatted phone for login:', formattedPhone.replace(/\d/g, '*'));

    try {
      const result = await apiRequest<{ user: any; token?: string }>('/api/auth/login', {
        method: 'POST',
        body: JSON.stringify({ phone: formattedPhone }),
      });
      console.log('Login successful:', { ...result, user: { ...result.user, phone: result.user?.phone?.replace(/\d/g, '*') } });
      return result;
    } catch (error) {
      console.error('Login failed:', error);
      throw error;
    }
  },
};

export const RegistrationAPI = {
  async startRegistration(data: {
    fullName: string;
    phone: string;
    email?: string;
  }): Promise<RegistrationStartResponse> {
    console.log('Starting registration with data:', { ...data, phone: data.phone.replace(/\d/g, '*') });
    
    const deviceId = await DeviceManager.getOrCreateDeviceId();
    console.log('Device ID:', deviceId);
    
    // Format phone number to include +972 if not present
    let formattedPhone = data.phone.replace(/[^0-9]/g, '');
    if (formattedPhone.startsWith('0')) {
      formattedPhone = '+972' + formattedPhone.slice(1);
    } else if (!formattedPhone.startsWith('972')) {
      formattedPhone = '+972' + formattedPhone;
    } else {
      formattedPhone = '+' + formattedPhone;
    }
    console.log('Formatted phone:', formattedPhone.replace(/\d/g, '*'));

    const requestData: RegistrationStartRequest = {
      fullName: data.fullName.trim(),
      phone: formattedPhone,
      email: data.email?.trim() || undefined,
      deviceId,
    };

    try {
      const result = await apiRequest<RegistrationStartResponse>('/api/registration/start', {
        method: 'POST',
        body: JSON.stringify(requestData),
      });
      console.log('Registration started successfully:', result);
      return result;
    } catch (error) {
      console.error('Registration start failed:', error);
      throw error;
    }
  },

  async completeRegistration(data: {
    city: string;
    street: string;
    buildingNumber: string;
    apartment?: string;
  }): Promise<RegistrationCompleteResponse> {
    const regId = await RegistrationManager.getRegId();
    if (!regId) {
      throw new APIError(400, 'No registration ID found. Please start registration again.');
    }

    const requestData: RegistrationCompleteRequest = {
      address: {
        city: data.city.trim(),
        street: data.street.trim(),
        buildingNumber: data.buildingNumber.trim(),
        apartment: data.apartment?.trim() || undefined,
      },
    };

    return apiRequest<RegistrationCompleteResponse>(`/api/registration/${regId}/complete`, {
      method: 'PATCH',
      body: JSON.stringify(requestData),
    });
  },

  async getRegistration(regId: string): Promise<any> {
    return apiRequest(`/api/registration/${regId}`);
  },
};

// Dev-only API for testing
export const DevAPI = {
  async listUsers(query?: string, limit = 20): Promise<UserListResponse> {
    if (!__DEV__) {
      throw new APIError(403, 'Dev API only available in development');
    }

    const params = new URLSearchParams();
    if (query) params.append('q', query);
    params.append('limit', limit.toString());

    const queryString = params.toString();
    const url = `/api/users${queryString ? `?${queryString}` : ''}`;

    return apiRequest<UserListResponse>(url, {
      headers: {
        'X-Admin-Token': DEV_ADMIN_TOKEN,
      },
    });
  },

  async checkHealth(): Promise<any> {
    return apiRequest('/healthz');
  },
};

// Utility functions
export const APIUtils = {
  isNetworkError: (error: APIError): boolean => error.status === 0,
  isServerError: (error: APIError): boolean => error.status >= 500,
  isClientError: (error: APIError): boolean => error.status >= 400 && error.status < 500,
  isDuplicatePhoneError: (error: APIError): boolean => error.code === 'DUPLICATE_PHONE',
  isAdminUser: (phone: string): boolean => {
    if (!__DEV__) return false;
    const cleanPhone = phone.replace(/[^0-9]/g, '');
    const cleanAdminPhone = DEV_ADMIN_PHONE.replace(/[^0-9]/g, '');
    return cleanPhone === cleanAdminPhone;
  },
  
  getErrorMessage: (error: unknown): string => {
    if (error instanceof APIError) {
      return error.message;
    }
    if (error instanceof Error) {
      return error.message;
    }
    return 'An unexpected error occurred';
  },
};