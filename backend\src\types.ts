/**
 * Shared types for the DLSA Backend API
 */

export interface Address {
  city: string;
  street: string;
  buildingNumber: string;
  apartment?: string;
}

export interface User {
  _id: string;
  fullName: string;
  phone: string;
  email?: string;
  address?: Address;
  status: "PENDING" | "COMPLETE";
  regStage: 0 | 1 | 2;
  deviceId: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface RegistrationStart {
  fullName: string;
  phone: string;
  email?: string;
  deviceId: string;
}

export interface RegistrationComplete {
  address: Address;
}

export interface UserListItem {
  _id: string;
  fullName: string;
  phone: string;
  email?: string;
  city?: string;
  createdAt: string;
}

export interface ApiResponse<T = any> {
  data?: T;
  error?: string;
  message?: string;
}

export interface UserListResponse {
  items: UserListItem[];
  nextCursor?: string | null;
}

export interface RegistrationStartResponse {
  regId: string;
  status: "PENDING";
}

export interface RegistrationCompleteResponse {
  status: "COMPLETE";
  userId: string;
  createdAt: string;
}
