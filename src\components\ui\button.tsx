import React, { useRef } from 'react';
import {
  Pressable,
  Text,
  StyleSheet,
  View,
  ActivityIndicator,
  Animated,
  Platform,
  PressableProps,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import * as Haptics from 'expo-haptics';
import { Ionicons } from '@expo/vector-icons';
import { tokens } from '@/src/core/theme/tokens';


interface ButtonProps extends Omit<PressableProps, 'style'> {
  variant?: 'primary' | 'secondary' | 'ghost' | 'danger' | 'success';
  size?: 'small' | 'medium' | 'large';
  children: React.ReactNode;
  loading?: boolean;
  disabled?: boolean;
  icon?: keyof typeof Ionicons.glyphMap;
  iconPosition?: 'left' | 'right';
  fullWidth?: boolean;
  style?: ViewStyle;
  textStyle?: TextStyle;
  haptic?: 'light' | 'medium' | 'heavy';
}

export const Button: React.FC<ButtonProps> = ({
  variant = 'primary',
  size = 'medium',
  children,
  loading = false,
  disabled = false,
  icon,
  iconPosition = 'left',
  fullWidth = false,
  style,
  textStyle,
  haptic = 'medium',
  onPress,
  ...props
}) => {
  const scaleAnim = useRef(new Animated.Value(1)).current;

  const handlePressIn = () => {
    Animated.spring(scaleAnim, {
      toValue: 0.97,
      useNativeDriver: true,
    }).start();
  };

  const handlePressOut = () => {
    Animated.spring(scaleAnim, {
      toValue: 1,
      useNativeDriver: true,
    }).start();
  };

  const handlePress = async (e: any) => {
    if (haptic === 'light') await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    if (haptic === 'medium') await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    if (haptic === 'heavy') await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
    onPress?.(e);
  };

  const isDisabled = disabled || loading;

  const renderContent = () => {
    const content = (
      <>
        {loading ? (
          <ActivityIndicator 
            color={variant === 'ghost' ? tokens.colors.primary : '#fff'} 
            size="small" 
          />
        ) : (
          <>
            {icon && iconPosition === 'left' && (
              <Ionicons 
                name={icon} 
                size={sizes[size].iconSize} 
                color={variants[variant].iconColor}
                style={{ marginRight: 8 }}
              />
            )}
            <Text style={[
              styles.text,
              sizes[size].text,
              variants[variant].text,
              textStyle,
            ]}>
              {children}
            </Text>
            {icon && iconPosition === 'right' && (
              <Ionicons 
                name={icon} 
                size={sizes[size].iconSize} 
                color={variants[variant].iconColor}
                style={{ marginLeft: 8 }}
              />
            )}
          </>
        )}
      </>
    );

    if (variant === 'primary' && !isDisabled) {
      return (
        <LinearGradient
          colors={['#445EFB', '#6EA8FF']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={[styles.content, sizes[size].content]}
        >
          {content}
        </LinearGradient>
      );
    }

    if (variant === 'secondary') {
      return (
        <BlurView 
          intensity={24} 
          tint="light" 
          style={[styles.content, sizes[size].content, variants[variant].container]}
        >
          {content}
        </BlurView>
      );
    }

    return (
      <View style={[styles.content, sizes[size].content, variants[variant].container]}>
        {content}
      </View>
    );
  };

  return (
    <Animated.View
      style={[
        fullWidth && styles.fullWidth,
        { transform: [{ scale: scaleAnim }] },
        style,
      ]}
    >
      <Pressable
        {...props}
        onPress={handlePress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        disabled={isDisabled}
        style={[
          styles.button,
          sizes[size].button,
          variants[variant].button,
          isDisabled && styles.disabled,
        ]}
      >
        {renderContent()}
      </Pressable>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  button: {
    borderRadius: 16,
    overflow: 'hidden',
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  text: {
    fontWeight: '700',
  },
  fullWidth: {
    width: '100%',
  },
  disabled: {
    opacity: 0.5,
  },
});

const sizes = {
  small: {
    button: { minHeight: 40 },
    content: { paddingHorizontal: 16, paddingVertical: 10 },
    text: { fontSize: 14 },
    iconSize: 16,
  },
  medium: {
    button: { minHeight: 56 },
    content: { paddingHorizontal: 24, paddingVertical: 16 },
    text: { fontSize: 16 },
    iconSize: 20,
  },
  large: {
    button: { minHeight: 64 },
    content: { paddingHorizontal: 32, paddingVertical: 20 },
    text: { fontSize: 18 },
    iconSize: 24,
  },
};

const variants = {
  primary: {
    button: {
      ...Platform.select({
        ios: tokens.shadows.primary,
        android: { 
        elevation: 8,
        shadowColor: '#445EFB',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.3,
        shadowRadius: 8,
      },
      }),
    },
    container: { backgroundColor: tokens.colors.primary },
    text: { color: tokens.colors.textWhite },
    iconColor: tokens.colors.textWhite,
  },
  secondary: {
    button: {
      borderWidth: 1,
      borderColor: 'rgba(0,0,0,0.06)',
      ...Platform.select({
        ios: tokens.shadows.sm,
        android: { 
        elevation: 8,
        shadowColor: '#445EFB',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.3,
        shadowRadius: 8,
      },
      }),
    },
    container: { backgroundColor: tokens.colors.surfaceGlass },
    text: { color: tokens.colors.textSecondary },
    iconColor: tokens.colors.textSecondary,
  },
  ghost: {
    button: {},
    container: { backgroundColor: 'transparent' },
    text: { color: tokens.colors.primary },
    iconColor: tokens.colors.primary,
  },
  danger: {
    button: {},
    container: { backgroundColor: tokens.colors.error },
    text: { color: tokens.colors.textWhite },
    iconColor: tokens.colors.textWhite,
  },
  success: {
    button: {},
    container: { backgroundColor: tokens.colors.success },
    text: { color: tokens.colors.textWhite },
    iconColor: tokens.colors.textWhite,
  },
};