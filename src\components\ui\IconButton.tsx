import React, { useRef } from 'react';
import {
  Pressable,
  StyleSheet,
  Animated,
  ViewStyle,
  View,
  Text,
} from 'react-native';
import { BlurView } from 'expo-blur';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import { tokens } from '../../core/theme/tokens';

interface IconButtonProps {
  icon: keyof typeof Ionicons.glyphMap;
  onPress: () => void;
  size?: 'small' | 'medium' | 'large';
  variant?: 'default' | 'blur' | 'primary' | 'ghost';
  disabled?: boolean;
  badge?: number | boolean;
  style?: ViewStyle;
}

export const IconButton: React.FC<IconButtonProps> = ({
  icon,
  onPress,
  size = 'medium',
  variant = 'default',
  disabled = false,
  badge,
  style,
}) => {
  const scaleAnim = useRef(new Animated.Value(1)).current;

  const handlePressIn = () => {
    Animated.spring(scaleAnim, {
      toValue: 0.9,
      useNativeDriver: true,
    }).start();
  };

  const handlePressOut = () => {
    Animated.spring(scaleAnim, {
      toValue: 1,
      useNativeDriver: true,
    }).start();
  };

  const handlePress = async () => {
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    onPress();
  };

  const renderContent = () => {
    const iconElement = (
      <>
        <Ionicons
          name={icon}
          size={sizeStyles[size].iconSize}
          color={variantStyles[variant].iconColor}
        />
        {badge !== undefined && (
          <View style={styles.badge}>
            {typeof badge === 'number' && (
              <Text style={styles.badgeText}>{badge > 99 ? '99+' : badge}</Text>
            )}
          </View>
        )}
      </>
    );

    if (variant === 'blur') {
      return (
        <BlurView
          intensity={20}
          tint="light"
          style={[styles.content, sizeStyles[size].content]}
        >
          {iconElement}
        </BlurView>
      );
    }

    return (
      <View style={[
        styles.content,
        sizeStyles[size].content,
        variantStyles[variant].content,
      ]}>
        {iconElement}
      </View>
    );
  };

  return (
    <Animated.View style={[{ transform: [{ scale: scaleAnim }] }, style]}>
      <Pressable
        onPress={handlePress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        disabled={disabled}
        style={[
          styles.button,
          sizeStyles[size].button,
          disabled && styles.disabled,
        ]}
      >
        {renderContent()}
      </Pressable>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  button: {
    borderRadius: 999,
    overflow: 'hidden',
  },
  content: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  disabled: {
    opacity: 0.5,
  },
  badge: {
    position: 'absolute',
    top: 0,
    right: 0,
    minWidth: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: tokens.colors.error,
    justifyContent: 'center',
    alignItems: 'center',
  },
  badgeText: {
    fontSize: 10,
    fontWeight: '700',
    color: tokens.colors.textWhite,
  },
});

const sizeStyles = {
  small: {
    button: { width: 32, height: 32 },
    content: { width: 32, height: 32 },
    iconSize: 16,
  },
  medium: {
    button: { width: 44, height: 44 },
    content: { width: 44, height: 44 },
    iconSize: 22,
  },
  large: {
    button: { width: 56, height: 56 },
    content: { width: 56, height: 56 },
    iconSize: 28,
  },
};

const variantStyles = {
  default: {
    content: { backgroundColor: '#F3F4F6' },
    iconColor: tokens.colors.textSecondary,
  },
  blur: {
    content: { backgroundColor: 'rgba(255,255,255,0.18)' },
    iconColor: tokens.colors.textWhite,
  },
  primary: {
    content: { backgroundColor: tokens.colors.primary },
    iconColor: tokens.colors.textWhite,
  },
  ghost: {
    content: { backgroundColor: 'transparent' },
    iconColor: tokens.colors.primary,
  },
};
