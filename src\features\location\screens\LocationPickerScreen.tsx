import React, { useEffect, useRef, useState, useCallback } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  Pressable,
  Animated,
  Dimensions,
  Platform,
  TextInput,
  Keyboard,
  PanResponder,
  StatusBar,
} from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { BlurView } from 'expo-blur';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import MapView, { Marker, PROVIDER_DEFAULT } from 'react-native-maps';
import * as Haptics from 'expo-haptics';
import { BottomSheetModalProvider } from '@gorhom/bottom-sheet';
import { LocationBottomSheet } from '../components/LocationBottomSheet';
// NEW imports for the sheet
import BottomSheet, {
  BottomSheetBackdrop,
  BottomSheetFlatList,
  BottomSheetTextInput,
} from "@gorhom/bottom-sheet";


// Components
import { But<PERSON> } from '@/src/components';
import { LocationCard } from '../components/LocationCard';
import { AddLocationSheet } from '../components/AddLocationSheet';
import { LocationService } from '@/src/core/services/location/LocationService';
import { SavedLocation } from '@/src/core/types/location.types';
import { useLocationPermissions } from '@/src/core/hooks/location/useLocationPermissions';
import { tokens } from '@/src/core/theme/tokens';
const [selectedLocation, setSelectedLocation] = useState<SavedLocation | null>(null);
const [savedLocations, setSavedLocations] = useState<SavedLocation[]>([]);
const [showAddLocation, setShowAddLocation] = useState(false);

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

// Instagram-style snap points
const SHEET_CLOSED = 130;
const SHEET_PEEK = SCREEN_HEIGHT * 0.4;
const SHEET_OPEN = SCREEN_HEIGHT * 0.85;

export default function LocationPickerScreen() {
  const router = useRouter();
  const insets = useSafeAreaInsets();
  const mapRef = useRef<MapView>(null);
  const { hasPermission, requestPermissions } = useLocationPermissions();

  // Animation values
  const translateY = useRef(new Animated.Value(SHEET_PEEK)).current;
  const lastGestureY = useRef(0);
  const fadeAnim = useRef(new Animated.Value(0)).current;

  // State
  const [selectedLocation, setSelectedLocation] = useState<SavedLocation | null>(null);
  const [savedLocations, setSavedLocations] = useState<SavedLocation[]>([]);
  const [currentLocation, setCurrentLocation] = useState<any>(null);
  const [showAddLocation, setShowAddLocation] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [sheetState, setSheetState] = useState<'closed' | 'peek' | 'open'>('peek');

  // Instagram-style pan responder
  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => false,
      onStartShouldSetPanResponderCapture: () => false,
      onMoveShouldSetPanResponder: (_, gestureState) => {
        // Only capture vertical swipes
        return Math.abs(gestureState.dy) > 5 && Math.abs(gestureState.dy) > Math.abs(gestureState.dx);
      },
      onMoveShouldSetPanResponderCapture: (_, gestureState) => {
        return Math.abs(gestureState.dy) > 5 && Math.abs(gestureState.dy) > Math.abs(gestureState.dx);
      },
      onPanResponderGrant: () => {
        lastGestureY.current = 0;
      },
      onPanResponderMove: (_, gestureState) => {
        const currentHeight = SCREEN_HEIGHT - translateY._value;
        const newHeight = currentHeight - gestureState.dy;
        
        // Rubber band effect at edges
        let dampening = 1;
        if (newHeight > SHEET_OPEN) {
          dampening = 0.3;
        } else if (newHeight < SHEET_CLOSED) {
          dampening = 0.5;
        }
        
        translateY.setValue(SCREEN_HEIGHT - (currentHeight - gestureState.dy * dampening));
        lastGestureY.current = gestureState.dy;
      },
      onPanResponderRelease: (_, gestureState) => {
        const currentHeight = SCREEN_HEIGHT - translateY._value;
        const velocity = -gestureState.vy;
        
        let targetHeight = SHEET_PEEK;
        let newState: 'closed' | 'peek' | 'open' = 'peek';
        
        // Instagram-style velocity-based snapping
        if (velocity > 0.5) {
          // Fast swipe up
          if (currentHeight < SHEET_OPEN - 100) {
            targetHeight = SHEET_OPEN;
            newState = 'open';
          } else {
            targetHeight = SHEET_PEEK;
            newState = 'peek';
          }
        } else if (velocity < -0.5) {
          // Fast swipe down
          if (currentHeight > SHEET_CLOSED + 100) {
            targetHeight = SHEET_CLOSED;
            newState = 'closed';
          } else {
            targetHeight = SHEET_PEEK;
            newState = 'peek';
          }
        } else {
          // Position-based snapping
          const distanceToClosed = Math.abs(currentHeight - SHEET_CLOSED);
          const distanceToPeek = Math.abs(currentHeight - SHEET_PEEK);
          const distanceToOpen = Math.abs(currentHeight - SHEET_OPEN);
          
          const minDistance = Math.min(distanceToClosed, distanceToPeek, distanceToOpen);
          
          if (minDistance === distanceToClosed) {
            targetHeight = SHEET_CLOSED;
            newState = 'closed';
          } else if (minDistance === distanceToOpen) {
            targetHeight = SHEET_OPEN;
            newState = 'open';
          } else {
            targetHeight = SHEET_PEEK;
            newState = 'peek';
          }
        }
        
        setSheetState(newState);
        
        Animated.spring(translateY, {
          toValue: SCREEN_HEIGHT - targetHeight,
          velocity,
          tension: 65,
          friction: 11,
          useNativeDriver: true,
        }).start();
        
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      },
    })
  ).current;

  useEffect(() => {
    initializeScreen();
  }, []);

  const initializeScreen = async () => {
    StatusBar.setBarStyle('dark-content');
    
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 400,
        useNativeDriver: true,
      }),
      Animated.spring(translateY, {
        toValue: SCREEN_HEIGHT - SHEET_PEEK,
        tension: 65,
        friction: 11,
        useNativeDriver: true,
      }),
    ]).start();

    await loadLocations();
    await getCurrentUserLocation();
  };

  const loadLocations = async () => {
    try {
      const locations = await LocationService.getSavedLocations();
      setSavedLocations(locations);
      
      const defaultLoc = locations.find(l => l.isDefault) || locations[0];
      if (defaultLoc) {
        setSelectedLocation(defaultLoc);
      }
    } catch (error) {
      console.error('Failed to load locations:', error);
    }
  };

  const getCurrentUserLocation = async () => {
    if (!hasPermission) {
      const granted = await requestPermissions();
      if (!granted) return;
    }

    try {
      const coords = await LocationService.getCurrentLocation();
      setCurrentLocation(coords);
      
      mapRef.current?.animateToRegion({
        ...coords,
        latitudeDelta: 0.01,
        longitudeDelta: 0.01,
      }, 500);
      
      if (!selectedLocation) {
        const address = await LocationService.reverseGeocode(coords);
        setSelectedLocation({
          id: 'current',
          label: 'מיקום נוכחי',
          address,
          coordinates: coords,
          type: 'current',
        });
      }
    } catch (error) {
      console.error('Error getting location:', error);
    }
  };

  const handleSelectCurrentLocation = async () => {
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    
    if (!currentLocation) {
      await getCurrentUserLocation();
      return;
    }
    
    const address = await LocationService.reverseGeocode(currentLocation);
    const currentLoc: SavedLocation = {
      id: 'current-' + Date.now(),
      label: 'מיקום נוכחי',
      address,
      coordinates: currentLocation,
      type: 'current',
    };
    
    setSelectedLocation(currentLoc);
    
    mapRef.current?.animateToRegion({
      ...currentLocation,
      latitudeDelta: 0.005,
      longitudeDelta: 0.005,
    }, 800);
  };

  const handleLocationSelect = (location: SavedLocation) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setSelectedLocation(location);
    
    mapRef.current?.animateToRegion({
      ...location.coordinates,
      latitudeDelta: 0.01,
      longitudeDelta: 0.01,
    }, 800);
  };

  const handleConfirmLocation = async () => {
    if (!selectedLocation) return;
    
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
    
    Animated.timing(fadeAnim, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      router.back();
    });
  };

  const handleMyLocation = async () => {
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    
    if (!currentLocation) {
      await getCurrentUserLocation();
      return;
    }
    
    mapRef.current?.animateToRegion({
      ...currentLocation,
      latitudeDelta: 0.005,
      longitudeDelta: 0.005,
    }, 800);
  };

  const handleSaveNewLocation = async (data: {
    name: string;
    city: string;
    street: string;
    number: string;
  }) => {
    const address = `${data.street} ${data.number}, ${data.city}`;
    
    // Try to geocode the address
    let coords = currentLocation;
    try {
      const results = await Location.geocodeAsync(address + ', Israel');
      if (results.length > 0) {
        coords = {
          latitude: results[0].latitude,
          longitude: results[0].longitude,
        };
      }
    } catch (error) {
      console.error('Geocoding failed:', error);
    }
    
    const newLocation: SavedLocation = {
      id: Date.now().toString(),
      label: data.name,
      address,
      coordinates: coords,
      type: 'custom',
    };
    
    await LocationService.saveLocation(newLocation);
    await loadLocations();
    setSelectedLocation(newLocation);
    setShowAddLocation(false);
  };

  // Calculate dynamic values
  const sheetHeight = translateY.interpolate({
    inputRange: [0, SCREEN_HEIGHT],
    outputRange: [SCREEN_HEIGHT, 0],
    extrapolate: 'clamp',
  });

  const backdropOpacity = translateY.interpolate({
    inputRange: [SCREEN_HEIGHT - SHEET_OPEN, SCREEN_HEIGHT - SHEET_PEEK],
    outputRange: [0.5, 0],
    extrapolate: 'clamp',
  });

  return (
    <View style={styles.container}>
  <Stack.Screen
    options={{
      headerShown: false,
      animation: "fade_from_bottom",
    }}
  />
  <StatusBar barStyle="dark-content" />

  {/* Map */}
  <Animated.View style={[styles.mapContainer, { opacity: fadeAnim }]}>
    <MapView
      ref={mapRef}
      style={styles.map}
      provider={PROVIDER_DEFAULT}
      showsUserLocation={true}
      showsMyLocationButton={false}
      showsCompass={false}
      onMapReady={getCurrentUserLocation}
      mapPadding={{
        top: insets.top + 56,
        bottom: 150,
        left: 0,
        right: 0,
      }}
    >
      {selectedLocation && (
        <Marker coordinate={selectedLocation.coordinates}>
          <View style={styles.markerContainer}>
            <View style={styles.markerDot} />
            <View style={styles.markerPulse} />
          </View>
        </Marker>
      )}
    </MapView>

    {/* My Location FAB */}
    <View style={[styles.myLocationFab, { bottom: 170 }]}>
      <Pressable
        style={({ pressed }) => [styles.fabButton, pressed && styles.fabPressed]}
        onPress={handleMyLocation}
      >
        <BlurView intensity={90} tint="light" style={styles.fabContent}>
          <MaterialIcons name="my-location" size={24} color={tokens.colors.primary} />
        </BlurView>
      </Pressable>
    </View>
  </Animated.View>

  {/* Fixed Header - No Gap */}
  <View style={[styles.header, { paddingTop: insets.top }]}>
    <BlurView intensity={95} tint="light" style={styles.headerContent}>
      <Pressable
        onPress={() => router.back()}
        style={({ pressed }) => [styles.backButton, pressed && styles.buttonPressed]}
      >
        <Ionicons name="chevron-back" size={28} color={tokens.colors.primary} />
      </Pressable>

      <Text style={styles.headerTitle}>בחר מיקום</Text>

      <Pressable
        onPress={() => router.back()}
        style={({ pressed }) => [styles.cancelButton, pressed && styles.buttonPressed]}
      >
        <Text style={styles.cancelText}>ביטול</Text>
      </Pressable>
    </BlurView>
  </View>

  {/* NEW: Instagram-like Bottom Sheet (replaces old Animated/PanResponder sheet + backdrop) */}
  <BottomSheet
    index={1}                                   // start at peek
    snapPoints={["16%", "48%", "92%"]}          // IG-like feel
    enablePanDownToClose={false}
    keyboardBehavior="interactive"
    backgroundStyle={{
      backgroundColor: tokens.colors.backgroundPure,
      borderTopLeftRadius: 24,
      borderTopRightRadius: 24,
    }}
    handleIndicatorStyle={{ backgroundColor: "#D1D5DB" }}
    backdropComponent={(props) => (
      <BottomSheetBackdrop
        {...props}
        appearsOnIndex={1}                      // show dim when open
        disappearsOnIndex={0}
        pressBehavior="collapse"                // tap backdrop → collapse to peek
      />
    )}
  >
    {/* Use current location */}
    <Pressable
      style={({ pressed }) => [styles.currentLocationButton, pressed && styles.buttonPressed]}
      onPress={handleSelectCurrentLocation}
    >
      <MaterialIcons name="my-location" size={20} color={tokens.colors.success} />
      <Text style={styles.currentLocationText}>השתמש במיקום הנוכחי</Text>
    </Pressable>

    {/* Search (keyboard-aware) */}
    <View style={styles.searchContainer}>
      <Ionicons name="search" size={20} color={tokens.colors.textTertiary} />
      <BottomSheetTextInput
        style={styles.searchInput}
        placeholder="חפש כתובת..."
        placeholderTextColor={tokens.colors.textTertiary}
        value={searchQuery}
        onChangeText={setSearchQuery}
        returnKeyType="search"
      />
      {searchQuery.length > 0 && (
        <Pressable onPress={() => setSearchQuery("")}>
          <Ionicons name="close-circle" size={20} color={tokens.colors.textTertiary} />
        </Pressable>
      )}
    </View>

    {/* Saved locations header + Add */}
    <View style={styles.sectionHeader}>
      <Text style={styles.sectionTitle}>מיקומים שמורים</Text>
      <Pressable
        onPress={() => setShowAddLocation(true)}
        style={({ pressed }) => [styles.addButton, pressed && styles.buttonPressed]}
      >
        <Ionicons name="add-circle" size={24} color={tokens.colors.primary} />
      </Pressable>
    </View>

    {/* Locations list (gesture-integrated) */}
    <BottomSheetFlatList
      data={savedLocations}
      keyExtractor={(item) => item.id}
      showsVerticalScrollIndicator={false}
      contentContainerStyle={{ paddingBottom: 120, paddingHorizontal: 20 }}
      renderItem={({ item, index }) => (
        <LocationCard
          location={item}
          isSelected={selectedLocation?.id === item.id}
          onPress={() => handleLocationSelect(item)}
          index={index}
        />
      )}
      ListEmptyComponent={
        <View style={{ paddingHorizontal: 20, paddingVertical: 24 }}>
          <Text style={{ color: tokens.colors.textSecondary }}>לא נמצאו מיקומים שמורים</Text>
        </View>
      }
    />

    {/* Sticky confirm */}
    <View style={[styles.confirmContainer, { paddingBottom: insets.bottom + 16 }]}>
      <Button
        variant="primary"
        size="large"
        fullWidth
        onPress={handleConfirmLocation}
        disabled={!selectedLocation}
      >
        אישור מיקום
      </Button>
    </View>
  </BottomSheet>

  {/* Add Location Sheet (unchanged) */}
  {showAddLocation && (
    <AddLocationSheet
      visible={showAddLocation}
      onClose={() => setShowAddLocation(false)}
      onSave={handleSaveNewLocation}
    />
  )}
</View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: tokens.colors.backgroundSurface,
  },
  mapContainer: {
    flex: 1,
  },
  map: {
    ...StyleSheet.absoluteFillObject,
  },

  // Header - Fixed to top with no gap
  header: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 100,
    backgroundColor: 'rgba(255, 255, 255, 0.98)',
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  backButton: {
    padding: 4,
  },
  headerTitle: {
    fontSize: 17,
    fontWeight: '600',
    color: tokens.colors.textPrimary,
  },
  cancelButton: {
    padding: 4,
  },
  cancelText: {
    fontSize: 17,
    color: tokens.colors.primary,
  },
  buttonPressed: {
    opacity: 0.6,
  },

  // Backdrop
  backdrop: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'black',
    zIndex: 90,
  },

  // Bottom Sheet - Instagram style
  bottomSheet: {
    position: 'absolute',
    left: 0,
    right: 0,
    height: SCREEN_HEIGHT,
    backgroundColor: tokens.colors.backgroundPure,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    zIndex: 100,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: -3 },
        shadowOpacity: 0.1,
        shadowRadius: 10,
      },
      android: {
        elevation: 20,
      },
    }),
  },
  handleContainer: {
    alignItems: 'center',
    paddingVertical: 10,
  },
  handle: {
    width: 40,
    height: 5,
    backgroundColor: '#D1D5DB',
    borderRadius: 3,
  },

  // Closed state
  closedContent: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  closedBar: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  closedText: {
    flex: 1,
    fontSize: 15,
    fontWeight: '500',
    color: tokens.colors.textPrimary,
  },

  // Expanded state
  expandedContent: {
    flex: 1,
  },
  currentLocationButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
    backgroundColor: tokens.colors.successBackground,
    marginHorizontal: 20,
    marginBottom: 12,
    paddingVertical: 14,
    borderRadius: tokens.borderRadius.lg,
  },
  currentLocationText: {
    fontSize: 16,
    fontWeight: '600',
    color: tokens.colors.success,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    backgroundColor: tokens.colors.backgroundSurface,
    marginHorizontal: 20,
    marginBottom: 16,
    paddingHorizontal: 16,
    height: 44,
    borderRadius: tokens.borderRadius.md,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: tokens.colors.textPrimary,
  },
  locationsList: {
    flex: 1,
    paddingHorizontal: 20,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 13,
    fontWeight: '600',
    color: tokens.colors.textSecondary,
    letterSpacing: 0.5,
  },
  addButton: {
    padding: 4,
  },
  confirmContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    paddingHorizontal: 20,
    paddingTop: 12,
    backgroundColor: tokens.colors.backgroundPure,
    borderTopWidth: StyleSheet.hairlineWidth,
    borderTopColor: tokens.colors.border,
  },

  // Map Marker
  markerContainer: {
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  markerDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: tokens.colors.primary,
    borderWidth: 2,
    borderColor: tokens.colors.backgroundPure,
    zIndex: 2,
  },
  markerPulse: {
    position: 'absolute',
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: tokens.colors.primary,
    opacity: 0.2,
  },

  // FAB
  myLocationFab: {
    position: 'absolute',
    right: 20,
    zIndex: 10,
  },
  fabButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    overflow: 'hidden',
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 4,
      },
    }),
  },
  fabPressed: {
    transform: [{ scale: 0.9 }],
  },
  fabContent: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
  },
});