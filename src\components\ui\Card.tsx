import React, { useRef, useEffect } from 'react';
import {
  View,
  StyleSheet,
  Animated,
  Pressable,
  ViewStyle,
  Platform,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import * as Haptics from 'expo-haptics';
import { tokens } from '../../core/theme/tokens';

interface CardProps {
  children: React.ReactNode;
  variant?: 'default' | 'gradient' | 'glass' | 'elevated';
  onPress?: () => void;
  style?: ViewStyle;
  animated?: boolean;
  floatingAnimation?: boolean;
  gradientColors?: string[];
  padding?: 'none' | 'small' | 'medium' | 'large';
}

export const Card: React.FC<CardProps> = ({
  children,
  variant = 'default',
  onPress,
  style,
  animated = true,
  floatingAnimation = false,
  gradientColors = ['#EFF6FF', '#DBEAFE'],
  padding = 'medium',
}) => {
  const scaleAnim = useRef(new Animated.Value(animated ? 0.95 : 1)).current;
  const floatAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (animated) {
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 30,
        friction: 8,
        useNativeDriver: true,
      }).start();
    }

    if (floatingAnimation) {
      Animated.loop(
        Animated.sequence([
          Animated.timing(floatAnim, {
            toValue: -10,
            duration: 2000,
            useNativeDriver: true,
          }),
          Animated.timing(floatAnim, {
            toValue: 0,
            duration: 2000,
            useNativeDriver: true,
          }),
        ])
      ).start();
    }
  }, []);

  const handlePress = async () => {
    if (onPress) {
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      Animated.sequence([
        Animated.timing(scaleAnim, {
          toValue: 0.95,
          duration: 100,
          useNativeDriver: true,
        }),
        Animated.spring(scaleAnim, {
          toValue: 1,
          tension: 30,
          friction: 8,
          useNativeDriver: true,
        }),
      ]).start();
      onPress();
    }
  };

  const content = (
    <View style={[styles.content, paddingStyles[padding]]}>
      {children}
    </View>
  );

  const renderCard = () => {
    switch (variant) {
      case 'gradient':
        return (
          <LinearGradient
            colors={gradientColors as any}
            style={[styles.card, variantStyles.gradient, style]}
          >
            {content}
          </LinearGradient>
        );
      
      case 'glass':
        return (
          <View style={[styles.card, variantStyles.glass, style]}>
            <BlurView intensity={20} tint="light" style={styles.blurView}>
              {content}
            </BlurView>
          </View>
        );
      
      case 'elevated':
        return (
          <View style={[styles.card, variantStyles.elevated, style]}>
            {content}
          </View>
        );
      
      default:
        return (
          <View style={[styles.card, variantStyles.default, style]}>
            {content}
          </View>
        );
    }
  };

  const animatedStyles = {
    transform: [
      { scale: scaleAnim },
      { translateY: floatingAnimation ? floatAnim : 0 },
    ],
  };

  if (onPress) {
    return (
      <Animated.View style={animatedStyles}>
        <Pressable onPress={handlePress}>
          {renderCard()}
        </Pressable>
      </Animated.View>
    );
  }

  return (
    <Animated.View style={animatedStyles}>
      {renderCard()}
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  card: {
    borderRadius: 20,
    overflow: 'hidden',
  },
  content: {
    width: '100%',
  },
  blurView: {
    flex: 1,
  },
});

const paddingStyles = {
  none: { padding: 0 },
  small: { padding: 12 },
  medium: { padding: 20 },
  large: { padding: 24 },
};

const variantStyles = StyleSheet.create({
  default: {
    backgroundColor: tokens.colors.backgroundPure,
    ...Platform.select({
      ios: tokens.shadows.md,
      android: {
         elevation: 4,
         shadowColor: '#445EFB',
         shadowOffset: { width: 0, height: 4 },
         shadowOpacity: 0.3,
         shadowRadius: 8,
      },
    }),
  },
  gradient: {
    ...Platform.select({
      ios: tokens.shadows.md,
      android: {
        elevation: 4,
        shadowColor: '#445EFB',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.3,
        shadowRadius: 8,
      },
    }),
  },
  glass: {
    backgroundColor: tokens.colors.surfaceGlass,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.06)',
    ...Platform.select({
      ios: tokens.shadows.sm,
      android: { 
        elevation: 2,
        shadowColor: '#445EFB',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.3,
        shadowRadius: 8,
      },
    }),
  },
  elevated: {
    backgroundColor: tokens.colors.backgroundPure,
    ...Platform.select({
      ios: tokens.shadows.lg,
      android: { 
        elevation: 8,
        shadowColor: '#445EFB',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.3,
        shadowRadius: 8,
      },
    }),
  },
});