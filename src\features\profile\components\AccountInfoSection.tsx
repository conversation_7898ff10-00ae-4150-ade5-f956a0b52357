// src/features/profile/components/AccountInfoSection.tsx
import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Card, ListItem, Badge, Divider } from '@/src/components';
import { tokens } from '@/src/core/theme/tokens';
import { Address } from '../types';

interface Props {
  user: {
    email: string;
    phone: string;
    address: Address;
  };
  onEditEmail: () => void;
  onEditPhone: () => void;
  onEditAddress: () => void;
}

export function AccountInfoSection({ user, onEditEmail, onEditPhone, onEditAddress }: Props) {
  const formatAddress = (addr: Address) => {
    return `${addr.street} ${addr.buildingNumber}${addr.apartment ? `/${addr.apartment}` : ''}, ${addr.city}`;
  };

  return (
    <Card variant="default" style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>פרטי חשבון</Text>
        <Badge variant="primary" size="small">מאומת</Badge>
      </View>
      
      <ListItem
        title={user.email}
        subtitle="כתובת אימייל"
        leftIcon="mail-outline"
        onPress={onEditEmail}
      />
      
      <Divider spacing="small" />
      
      <ListItem
        title={user.phone}
        subtitle="מספר טלפון"
        leftIcon="call-outline"
        onPress={onEditPhone}
      />
      
      <Divider spacing="small" />
      
      <ListItem
        title={formatAddress(user.address)}
        subtitle="כתובת ברירת מחדל"
        leftIcon="location-outline"
        onPress={onEditAddress}
      />
    </Card>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  title: {
    fontSize: 18,
    fontWeight: '700',
    color: tokens.colors.textPrimary,
  },
});