import React, { useRef, useState } from "react";
import { View, Text, TextInput, StyleSheet, Pressable, Animated, KeyboardAvoidingView, Platform } from "react-native";
import { Stack, useRouter } from "expo-router";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { LinearGradient } from "expo-linear-gradient";
import { Ionicons } from "@expo/vector-icons";
import * as Haptics from "expo-haptics";
import { tokens } from "@/src/core/theme/tokens";
import { LocationService } from "@/src/core/services/location/LocationService";

export default function AddLocationScreen() {
  const router = useRouter();
  const insets = useSafeAreaInsets();
  const [name, setName] = useState("");
  const [city, setCity] = useState("");
  const [street, setStreet] = useState("");
  const [number, setNumber] = useState("");
  const scaleAnim = useRef(new Animated.Value(1)).current;

  const canSave =
    name.trim().length > 0 &&
    city.trim().length > 0 &&
    street.trim().length > 0 &&
    number.trim().length > 0;

  const onSave = async () => {
    if (!canSave) return;
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

    const composed = `${street.trim()} ${number.trim()}, ${city.trim()}`;

    // Try forward geocoding; gracefully fallback later if needed.
    let coords: any = undefined;
    try {
      coords = await (LocationService as any).forwardGeocode?.(composed);
    } catch {}

    const newLocation = {
      id: Date.now().toString(),
      label: name.trim(),
      address: composed,
      coordinates: coords || undefined,
      type: "custom" as const,
    };

    await LocationService.saveLocation(newLocation);
    router.back();
  };

  return (
    <View style={styles.container}>
      <Stack.Screen options={{ headerShown: false }} />
      {/* Sticky header */}
      <View style={[styles.header, { paddingTop: insets.top }]}>
        <Pressable onPress={() => router.back()} style={({ pressed }) => [styles.headerBtn, pressed && { opacity: 0.6 }]}>
          <Ionicons name="chevron-back" size={26} color={tokens.colors.primary} />
        </Pressable>
        <Text style={styles.headerTitle}>מיקום חדש</Text>
        <View style={styles.headerBtn} />
      </View>

      <KeyboardAvoidingView behavior={Platform.OS === "ios" ? "padding" : "height"} style={styles.body}>
        {/* Address sections */}
        <View style={styles.row}>
          <View style={[styles.inputGroup, { flex: 1 }]}>
            <Text style={styles.label}>עיר</Text>
            <TextInput
              style={styles.input}
              placeholder="לדוגמה: תל אביב"
              placeholderTextColor="#9CA3AF"
              value={city}
              onChangeText={setCity}
              autoCorrect
            />
          </View>
        </View>

        <View style={styles.row}>
          <View style={[styles.inputGroup, { flex: 1.3 }]}>
            <Text style={styles.label}>רחוב</Text>
            <TextInput
              style={styles.input}
              placeholder="לדוגמה: בן יהודה"
              placeholderTextColor="#9CA3AF"
              value={street}
              onChangeText={setStreet}
              autoCorrect
            />
          </View>
          <View style={{ width: 12 }} />
          <View style={[styles.inputGroup, { flex: 0.7 }]}>
            <Text style={styles.label}>מספר</Text>
            <TextInput
              style={styles.input}
              placeholder="44"
              placeholderTextColor="#9CA3AF"
              value={number}
              onChangeText={setNumber}
              keyboardType="number-pad"
            />
          </View>
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>שם המיקום</Text>
          <TextInput
            style={styles.input}
            placeholder="לדוגמה: בית של סבתא"
            placeholderTextColor="#9CA3AF"
            value={name}
            onChangeText={setName}
          />
        </View>

        <View style={{ flex: 1 }} />

        {/* Bottom action */}
        <View style={styles.bottom}>
          <Pressable
            onPress={onSave}
            disabled={!canSave}
            style={({ pressed }) => [{ opacity: pressed || !canSave ? 0.7 : 1 }]}
          >
            <LinearGradient colors={["#445EFB", "#6EA8FF"]} style={styles.cta}>
              <Text style={styles.ctaText}>שמור מיקום</Text>
              <Ionicons name="checkmark" size={20} color="#FFFFFF" />
            </LinearGradient>
          </Pressable>
        </View>
      </KeyboardAvoidingView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: tokens.colors.backgroundPure },
  header: {
    height: 56 + 12,
    paddingHorizontal: 16,
    paddingBottom: 12,
    flexDirection: "row",
    alignItems: "flex-end",
    justifyContent: "space-between",
    backgroundColor: "rgba(255,255,255,0.95)",
  },
  headerBtn: { width: 36, alignItems: "center", justifyContent: "center" },
  headerTitle: { fontSize: 17, fontWeight: "700", color: tokens.colors.textPrimary },

  body: { flex: 1, paddingHorizontal: 20, paddingTop: 16 },
  row: { flexDirection: 'row' },
  inputGroup: { marginBottom: 22 },
  label: { fontSize: 14, fontWeight: "600", color: "#374151", marginBottom: 8 },
  input: {
    backgroundColor: "#F9FAFB",
    borderWidth: 1.5,
    borderColor: "#E5E7EB",
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 14,
    fontSize: 16,
    color: "#1F2937",
  },

  bottom: {
    paddingVertical: 8,
    paddingBottom: 24,
  },
  cta: {
    minHeight: 52,
    borderRadius: 14,
    paddingHorizontal: 16,
    paddingVertical: 12,
    alignItems: "center",
    justifyContent: "center",
    flexDirection: "row",
    gap: 6,
  },
  ctaText: { fontSize: 16, fontWeight: "700", color: "#fff" },
});
