import React from 'react';
import {
  View,
  Text,
  Pressable,
  StyleSheet,
  ViewStyle,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import { tokens } from '../../core/theme/tokens';

interface ListItemProps {
  title: string;
  subtitle?: string;
  leftIcon?: keyof typeof Ionicons.glyphMap;
  rightIcon?: keyof typeof Ionicons.glyphMap;
  leftElement?: React.ReactNode;
  rightElement?: React.ReactNode;
  onPress?: () => void;
  variant?: 'default' | 'card';
  style?: ViewStyle;
}

export const ListItem: React.FC<ListItemProps> = ({
  title,
  subtitle,
  leftIcon,
  rightIcon = 'chevron-forward',
  leftElement,
  rightElement,
  onPress,
  variant = 'default',
  style,
}) => {
  const handlePress = async () => {
    if (onPress) {
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      onPress();
    }
  };

  const content = (
    <>
      {(leftIcon || leftElement) && (
        <View style={styles.leftSection}>
          {leftElement || (
            <View style={styles.iconWrapper}>
              <Ionicons name={leftIcon!} size={22} color={tokens.colors.primary} />
            </View>
          )}
        </View>
      )}
      
      <View style={styles.contentSection}>
        <Text style={styles.title}>{title}</Text>
        {subtitle && <Text style={styles.subtitle}>{subtitle}</Text>}
      </View>
      
      {(rightIcon || rightElement) && (
        <View style={styles.rightSection}>
          {rightElement || (
            <Ionicons name={rightIcon!} size={20} color="#9CA3AF" />
          )}
        </View>
      )}
    </>
  );

  if (onPress) {
    return (
      <Pressable
        onPress={handlePress}
        style={({ pressed }) => [
          styles.container,
          variantStyles[variant],
          pressed && styles.pressed,
          style,
        ]}
      >
        {content}
      </Pressable>
    );
  }

  return (
    <View style={[styles.container, variantStyles[variant], style]}>
      {content}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  pressed: {
    opacity: 0.7,
  },
  leftSection: {
    marginRight: 12,
  },
  iconWrapper: {
    width: 40,
    height: 40,
    borderRadius: 12,
    backgroundColor: tokens.colors.infoBackground,
    justifyContent: 'center',
    alignItems: 'center',
  },
  contentSection: {
    flex: 1,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: tokens.colors.textPrimary,
  },
  subtitle: {
    fontSize: 14,
    color: tokens.colors.textTertiary,
    marginTop: 2,
  },
  rightSection: {
    marginLeft: 12,
  },
});

const variantStyles = {
  default: {},
  card: {
    backgroundColor: tokens.colors.backgroundPure,
    borderRadius: 16,
    marginHorizontal: 16,
    marginVertical: 4,
    ...Platform.select({
      ios: tokens.shadows.sm,
      android: {
        elevation: 2,
        shadowColor: '#445EFB',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.3,
        shadowRadius: 8,
      },
    }),
  },
};
