// src/features/dashboard/screens/DashboardScreen.tsx
import { <PERSON>ack, useRouter } from "expo-router";
import React, { useEffect, useRef, useState } from "react";
import {
  Animated,
  ScrollView,
  StyleSheet,
  View,
  StatusBar,
  RefreshControl,
  Pressable,
  Text,
  Alert,
} from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import * as Haptics from "expo-haptics";

// Components
import { DashboardHeader } from "../components/DashboardHeader";
import { NextLessonCard } from "../components/NextLessonCard";
import { BookLessonCard } from "../components/BookLessonCard";
import { QuickActionsGrid } from "../components/QuickActionsGrid";
import { InstructorCard } from "../components/InstructorCard";
import { TestCountdownCard } from "../components/TestCountdownCard";

// Services
import { UserManager } from "@/services/api";

// Future database service (uncomment when ready)
// import { LessonService } from "@/src/core/services/lessons/LessonService";
// import { supabase } from "@/src/core/services/supabase";

// Constants
const HEADER_MAX_HEIGHT = 180;
const HEADER_MIN_HEIGHT = 56;

// Mock data - replace with real API calls
const mockInstructor = {
  id: "instructor-1", // Add ID for database
  name: "דוד כהן",
  phone: "************",
  rating: 4.8,
  lessonsCompleted: 12,
  nextAvailable: "היום 14:00",
  nextLesson: {
    id: "lesson-1", // Add ID for database
    date: "יום ראשון",
    time: "10:00",
    location: "רחוב הרצל 12, תל אביב",
    duration: "90 דקות",
    type: "נסיעה עירונית",
  },
};

const mockProgress = {
  totalLessons: 28,
  completedLessons: 12,
  hoursCompleted: 18,
  testDate: "15.02.2025",
  daysUntilTest: 27,
};

export default function Dashboard() {
  const router = useRouter();
  const insets = useSafeAreaInsets();
  const [user, setUser] = useState<any>(null);
  const [refreshing, setRefreshing] = useState(false);
  const [currentTime, setCurrentTime] = useState(new Date());
  
  // Lesson state management
  const [hasUpcomingLesson, setHasUpcomingLesson] = useState(true); // Toggle this to test booking card
  const [nextLesson, setNextLesson] = useState<any>(null);
  const [isLoadingLesson, setIsLoadingLesson] = useState(false);

  // Animation values
  const scrollY = useRef(new Animated.Value(0)).current;
  const card1Opacity = useRef(new Animated.Value(0)).current;
  const card1TranslateY = useRef(new Animated.Value(30)).current;
  const card2Opacity = useRef(new Animated.Value(0)).current;
  const card2TranslateY = useRef(new Animated.Value(30)).current;
  const card3Opacity = useRef(new Animated.Value(0)).current;
  const card3TranslateY = useRef(new Animated.Value(30)).current;
  const quickActionsScale = useRef(new Animated.Value(0.8)).current;

  useEffect(() => {
    loadUserData();
    fetchLessonStatus(); // Fetch lesson status on mount

    // Update time every minute
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000);

    // Entrance animations
    Animated.stagger(150, [
      Animated.parallel([
        Animated.timing(card1Opacity, {
          toValue: 1,
          duration: 500,
          useNativeDriver: true,
        }),
        Animated.spring(card1TranslateY, {
          toValue: 0,
          tension: 30,
          friction: 8,
          useNativeDriver: true,
        }),
      ]),
      Animated.parallel([
        Animated.timing(card2Opacity, {
          toValue: 1,
          duration: 500,
          useNativeDriver: true,
        }),
        Animated.spring(card2TranslateY, {
          toValue: 0,
          tension: 30,
          friction: 8,
          useNativeDriver: true,
        }),
      ]),
      Animated.parallel([
        Animated.timing(card3Opacity, {
          toValue: 1,
          duration: 500,
          useNativeDriver: true,
        }),
        Animated.spring(card3TranslateY, {
          toValue: 0,
          tension: 30,
          friction: 8,
          useNativeDriver: true,
        }),
      ]),
      Animated.spring(quickActionsScale, {
        toValue: 1,
        tension: 20,
        friction: 7,
        useNativeDriver: true,
      }),
    ]).start();

    return () => clearInterval(timer);
  }, []);

  const loadUserData = async () => {
    try {
      const userData = await UserManager.getUser();
      setUser(userData);
    } catch (error) {
      console.error("Failed to load user data:", error);
    }
  };

  // Fetch lesson status from database (or mock)
  const fetchLessonStatus = async () => {
    setIsLoadingLesson(true);
    try {
      // TODO: Replace with real database call when ready
      /* 
      const { data, error } = await supabase
        .from('lessons')
        .select(`
          *,
          instructor:instructors(*)
        `)
        .eq('student_id', user?.id)
        .eq('status', 'scheduled')
        .gte('scheduled_date', new Date().toISOString())
        .order('scheduled_date', { ascending: true })
        .order('start_time', { ascending: true })
        .limit(1)
        .single();
      
      if (data) {
        setHasUpcomingLesson(true);
        setNextLesson(data);
      } else {
        setHasUpcomingLesson(false);
        setNextLesson(null);
      }
      */
      
      // Mock implementation for testing
      // Change this to false to see the booking card
      const mockHasLesson = true; 
      
      setHasUpcomingLesson(mockHasLesson);
      
      if (mockHasLesson) {
        setNextLesson(mockInstructor.nextLesson);
      } else {
        setNextLesson(null);
      }
    } catch (error) {
      console.error("Error fetching lesson status:", error);
      // Default to showing booking card on error
      setHasUpcomingLesson(false);
    } finally {
      setIsLoadingLesson(false);
    }
  };

  // Handle lesson cancellation
  const handleCancelLesson = async () => {
    try {
      setIsLoadingLesson(true);
      
      // TODO: Replace with real database call when ready
      /*
      await supabase
        .from('lessons')
        .update({ 
          status: 'cancelled',
          cancelled_at: new Date().toISOString(),
          cancellation_reason: 'student_cancelled'
        })
        .eq('id', nextLesson?.id);
      */
      
      // Mock implementation
      await new Promise(resolve => setTimeout(resolve, 500)); // Simulate API delay
      
      setHasUpcomingLesson(false);
      setNextLesson(null);
      
      // Show success message
      Alert.alert(
        "השיעור בוטל",
        "תוכל לקבוע שיעור חדש בכל עת",
        [{ text: "אישור", style: "default" }]
      );
    } catch (error) {
      console.error("Error cancelling lesson:", error);
      Alert.alert(
        "שגיאה",
        "לא הצלחנו לבטל את השיעור. אנא נסה שוב.",
        [{ text: "אישור", style: "default" }]
      );
    } finally {
      setIsLoadingLesson(false);
    }
  };

  // Handle booking a new lesson
  const handleBookLesson = async (slot: any) => {
    try {
      setIsLoadingLesson(true);
      
      // TODO: Replace with real database call when ready
      /*
      const { data, error } = await supabase
        .from('lessons')
        .insert({
          student_id: user?.id,
          instructor_id: mockInstructor.id,
          scheduled_date: slot.date,
          start_time: slot.time,
          end_time: calculateEndTime(slot.time, 90),
          status: 'scheduled',
          pickup_location: {
            address: user?.address 
              ? `${user.address.street} ${user.address.buildingNumber}, ${user.address.city}`
              : "רחוב הרצל 12, תל אביב"
          },
          lesson_type: 'נסיעה עירונית',
          duration_minutes: 90,
        })
        .select()
        .single();
      
      if (error) throw error;
      
      setHasUpcomingLesson(true);
      setNextLesson(data);
      */
      
      // Mock implementation
      await new Promise(resolve => setTimeout(resolve, 800)); // Simulate API delay
      
      const newLesson = {
        id: `lesson-${Date.now()}`,
        date: slot.date,
        time: slot.time,
        location: "רחוב הרצל 12, תל אביב",
        duration: "90 דקות",
        type: "נסיעה עירונית",
      };
      
      setHasUpcomingLesson(true);
      setNextLesson(newLesson);
      
      Alert.alert(
        "מעולה! 🎉",
        `השיעור נקבע ל${slot.date} בשעה ${slot.time}`,
        [{ text: "אישור", style: "default" }]
      );
    } catch (error) {
      console.error("Error booking lesson:", error);
      Alert.alert(
        "שגיאה",
        "לא הצלחנו לקבוע את השיעור. אנא נסה שוב.",
        [{ text: "אישור", style: "default" }]
      );
    } finally {
      setIsLoadingLesson(false);
    }
  };

  const onRefresh = React.useCallback(() => {
    setRefreshing(true);
    
    // Refresh all data
    Promise.all([
      loadUserData(),
      fetchLessonStatus(),
    ]).finally(() => {
      setRefreshing(false);
    });
  }, []);

  const getGreeting = () => {
    const hour = currentTime.getHours();
    if (hour < 12) return "בוקר טוב";
    if (hour < 17) return "צהריים טובים";
    if (hour < 21) return "ערב טוב";
    return "לילה טוב";
  };

  const navigateTo = async (route: string) => {
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    router.push(route as any);
  };

  return (
    <>
      <Stack.Screen options={{ headerShown: false }} />
      <StatusBar barStyle="light-content" />

      <View style={styles.container}>
        <DashboardHeader
          insets={insets}
          scrollY={scrollY}
          user={user}
          greeting={getGreeting()}
          progress={mockProgress}
          onNotifications={() => navigateTo("/(main)/notifications")}
          onProfile={() => navigateTo("/(main)/profile")}
        />
        
        <Animated.ScrollView
          style={styles.content}
          contentContainerStyle={[
            styles.scrollContent,
            { paddingTop: HEADER_MAX_HEIGHT + insets.top + 26 }
          ]}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl 
              refreshing={refreshing} 
              onRefresh={onRefresh} 
              tintColor="#445EFB"
              progressViewOffset={HEADER_MAX_HEIGHT + 20}
            />
          }
          onScroll={Animated.event(
            [{ nativeEvent: { contentOffset: { y: scrollY } } }],
            { useNativeDriver: false }
          )}
          scrollEventThrottle={16}
          bounces={true}
        >
          {/* Lesson Card - Shows either booked lesson or booking card */}
          <Animated.View
            style={{
              opacity: card1Opacity,
              transform: [{ translateY: card1TranslateY }],
            }}
          >
            {hasUpcomingLesson ? (
              <NextLessonCard
                lesson={nextLesson || mockInstructor.nextLesson}
                instructor={mockInstructor}
                onReschedule={() => navigateTo("/(main)/reschedule")}
                onNavigate={() => navigateTo("/(main)/navigate")}
                onCancelLesson={handleCancelLesson}
                onLocationPress={() => navigateTo("/(main)/location-picker")}
              />
            ) : (
              <BookLessonCard
                instructor={{
                  name: mockInstructor.name,
                  nextAvailable: mockInstructor.nextAvailable,
                }}
                onBookLesson={handleBookLesson}
                suggestedSlots={[
                  { id: "1", date: "היום", time: "14:00", available: true },
                  { id: "2", date: "היום", time: "16:00", available: true },
                  { id: "3", date: "מחר", time: "10:00", available: true },
                  { id: "4", date: "מחר", time: "12:00", available: false },
                  { id: "5", date: "ראשון", time: "09:00", available: true },
                  { id: "6", date: "ראשון", time: "11:00", available: true },
                ]}
              />
            )}
          </Animated.View>

          {/* Quick Actions Grid */}
          <Animated.View
            style={{
              transform: [{ scale: quickActionsScale }],
            }}
          >
            <QuickActionsGrid onNavigate={navigateTo} />
          </Animated.View>

          {/* Instructor Card */}
          <Animated.View
            style={{
              opacity: card2Opacity,
              transform: [{ translateY: card2TranslateY }],
            }}
          >
            <InstructorCard
              instructor={mockInstructor}
              progress={mockProgress}
              onViewProfile={() => navigateTo("/(main)/instructor-profile")}
              onChat={() => navigateTo("/(main)/chat")}
            />
          </Animated.View>

          {/* Test Countdown Card */}
          <Animated.View
            style={{
              opacity: card3Opacity,
              transform: [{ translateY: card3TranslateY }],
            }}
          >
            <TestCountdownCard
              testDate={mockProgress.testDate}
              daysUntilTest={mockProgress.daysUntilTest}
              onViewTips={() => navigateTo("/(main)/test-prep")}
            />
          </Animated.View>

          {/* Dev Mode Controls */}
          {__DEV__ && (
            <View style={styles.devSection}>
              <Text style={styles.devTitle}>Developer Controls</Text>
              
              <Pressable
                onPress={() => {
                  setHasUpcomingLesson(!hasUpcomingLesson);
                  if (!hasUpcomingLesson) {
                    setNextLesson(mockInstructor.nextLesson);
                  }
                }}
                style={styles.devButton}
              >
                <Text style={styles.devButtonText}>
                  Toggle Lesson Status (Current: {hasUpcomingLesson ? "Has Lesson" : "No Lesson"})
                </Text>
              </Pressable>
              
              <Pressable
                onPress={() => navigateTo("/(auth)/dev-users")}
                style={styles.devButton}
              >
                <Text style={styles.devButtonText}>Dev: View Users</Text>
              </Pressable>
            </View>
          )}
        </Animated.ScrollView>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F9FAFB",
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: 20,
    paddingBottom: 100,
  },
  devSection: {
    marginTop: 20,
    padding: 16,
    backgroundColor: "#F3F4F6",
    borderRadius: 12,
  },
  devTitle: {
    fontSize: 14,
    fontWeight: "600",
    color: "#6B7280",
    marginBottom: 12,
    textAlign: "center",
  },
  devButton: {
    alignItems: "center",
    paddingVertical: 12,
    backgroundColor: "#E5E7EB",
    borderRadius: 8,
    marginBottom: 8,
  },
  devButtonText: {
    fontSize: 12,
    color: "#4B5563",
    fontWeight: "600",
  },
});