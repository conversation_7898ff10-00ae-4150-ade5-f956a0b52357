import React from 'react';
import { View, Text, StyleSheet, Pressable, Platform } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';

interface NotificationCardProps {
  title: string;
  message: string;
  time: string;
  type?: 'info' | 'success' | 'warning' | 'error';
  read?: boolean;
  onPress?: () => void;
  onDismiss?: () => void;
}

export const NotificationCard: React.FC<NotificationCardProps> = ({
  title,
  message,
  time,
  type = 'info',
  read = false,
  onPress,
  onDismiss,
}) => {
  const handlePress = async () => {
    if (onPress) {
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      onPress();
    }
  };

  const handleDismiss = async () => {
    if (onDismiss) {
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      onDismiss();
    }
  };

  const typeConfig = {
    info: { icon: 'information-circle', color: '#3B82F6', bg: '#EFF6FF' },
    success: { icon: 'checkmark-circle', color: '#10B981', bg: '#D1FAE5' },
    warning: { icon: 'warning', color: '#F59E0B', bg: '#FEF3C7' },
    error: { icon: 'alert-circle', color: '#EF4444', bg: '#FEE2E2' },
  };

  const config = typeConfig[type];

  return (
    <Pressable
      onPress={handlePress}
      style={({ pressed }) => [
        styles.card,
        read && styles.readCard,
        pressed && styles.pressed,
      ]}
    >
      <View style={[styles.iconContainer, { backgroundColor: config.bg }]}>
        <Ionicons name={config.icon as any} size={24} color={config.color} />
      </View>
      
      <View style={styles.content}>
        <View style={styles.header}>
          <Text style={[styles.title, read && styles.readText]}>{title}</Text>
          {!read && <View style={styles.unreadDot} />}
        </View>
        <Text style={[styles.message, read && styles.readText]} numberOfLines={2}>
          {message}
        </Text>
        <Text style={styles.time}>{time}</Text>
      </View>
      
      {onDismiss && (
        <Pressable onPress={handleDismiss} style={styles.dismissButton}>
          <Ionicons name="close" size={20} color="#9CA3AF" />
        </Pressable>
      )}
    </Pressable>
  );
};

const styles = StyleSheet.create({
  card: {
    flexDirection: 'row',
    padding: 16,
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    marginBottom: 12,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.08,
        shadowRadius: 8,
      },
      android: {
        elevation: 3,
      },
    }),
  },
  readCard: {
    opacity: 0.7,
  },
  pressed: {
    opacity: 0.9,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  content: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  title: {
    fontSize: 16,
    fontWeight: '700',
    color: '#1F2937',
    flex: 1,
  },
  unreadDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#3B82F6',
    marginLeft: 8,
  },
  message: {
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 20,
    marginBottom: 4,
  },
  time: {
    fontSize: 12,
    color: '#9CA3AF',
    fontWeight: '500',
  },
  readText: {
    color: '#9CA3AF',
  },
  dismissButton: {
    padding: 4,
  },
});