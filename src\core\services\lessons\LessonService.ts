// src/core/services/lessons/LessonService.ts
import { supabase } from '../supabase';

export interface Lesson {
  id: string;
  student_id: string;
  instructor_id: string;
  scheduled_date: string;
  start_time: string;
  end_time: string;
  status: 'scheduled' | 'completed' | 'cancelled' | 'no_show';
  pickup_location: {
    address: string;
    coordinates?: { lat: number; lng: number };
  };
  lesson_type: string;
  duration_minutes: number;
  notes?: string;
  created_at: string;
  updated_at: string;
}

export interface TimeSlot {
  id: string;
  date: string;
  time: string;
  available: boolean;
}

export class LessonService {
  // Get next upcoming lesson for student
  static async getNextLesson(studentId: string): Promise<Lesson | null> {
    const { data, error } = await supabase
      .from('lessons')
      .select(`
        *,
        instructor:instructors(*)
      `)
      .eq('student_id', studentId)
      .eq('status', 'scheduled')
      .gte('scheduled_date', new Date().toISOString().split('T')[0])
      .order('scheduled_date', { ascending: true })
      .order('start_time', { ascending: true })
      .limit(1)
      .single();

    if (error || !data) return null;
    return data;
  }

  // Get available time slots for instructor
  static async getAvailableSlots(
    instructorId: string, 
    startDate?: Date
  ): Promise<TimeSlot[]> {
    // This would check instructor's availability_slots table
    // and cross-reference with existing lessons
    const { data: slots } = await supabase
      .from('availability_slots')
      .select('*')
      .eq('instructor_id', instructorId);

    // TODO: Complex logic to calculate available times
    // considering existing bookings, breaks, etc.
    
    return [];
  }

  // Book a new lesson
  static async bookLesson(
    studentId: string,
    instructorId: string,
    slot: TimeSlot,
    pickupLocation: string
  ): Promise<Lesson> {
    const { data, error } = await supabase
      .from('lessons')
      .insert({
        student_id: studentId,
        instructor_id: instructorId,
        scheduled_date: slot.date,
        start_time: slot.time,
        end_time: this.calculateEndTime(slot.time, 90), // 90 min default
        status: 'scheduled',
        pickup_location: {
          address: pickupLocation
        },
        lesson_type: 'נסיעה עירונית',
        duration_minutes: 90,
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  // Cancel a lesson
  static async cancelLesson(lessonId: string): Promise<void> {
    const { error } = await supabase
      .from('lessons')
      .update({ 
        status: 'cancelled',
        updated_at: new Date().toISOString()
      })
      .eq('id', lessonId);

    if (error) throw error;
  }

  // Reschedule a lesson
  static async rescheduleLesson(
    lessonId: string, 
    newSlot: TimeSlot
  ): Promise<Lesson> {
    const { data, error } = await supabase
      .from('lessons')
      .update({
        scheduled_date: newSlot.date,
        start_time: newSlot.time,
        updated_at: new Date().toISOString()
      })
      .eq('id', lessonId)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  // Helper to calculate end time
  private static calculateEndTime(startTime: string, durationMinutes: number): string {
    const [hours, minutes] = startTime.split(':').map(Number);
    const totalMinutes = hours * 60 + minutes + durationMinutes;
    const endHours = Math.floor(totalMinutes / 60);
    const endMinutes = totalMinutes % 60;
    return `${endHours.toString().padStart(2, '0')}:${endMinutes.toString().padStart(2, '0')}`;
  }
}