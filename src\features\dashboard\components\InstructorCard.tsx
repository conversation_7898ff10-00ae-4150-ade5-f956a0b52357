// src/features/dashboard/components/InstructorCard.tsx
import React from "react";
import {
  View,
  Text,
  Pressable,
  StyleSheet,
  Platform,
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { Ionicons, MaterialIcons } from "@expo/vector-icons";

interface Props {
  instructor: {
    name: string;
    rating: number;
    lessonsCompleted: number;
  };
  progress: {
    hoursCompleted: number;
    daysUntilTest: number;
  };
  onViewProfile: () => void;
  onChat: () => void;
}

export function InstructorCard({ instructor, progress, onViewProfile, onChat }: Props) {
  return (
    <View style={styles.card}>
      <View style={styles.content}>
        <View style={styles.header}>
          <View style={styles.avatar}>
            <Text style={styles.initial}>
              {instructor.name.charAt(0)}
            </Text>
          </View>
          <View style={styles.info}>
            <Text style={styles.name}>{instructor.name}</Text>
            <Text style={styles.role}>מורה נהיגה מוסמך</Text>
            <View style={styles.rating}>
              {[...Array(5)].map((_, i) => (
                <Ionicons
                  key={i}
                  name="star"
                  size={14}
                  color={i < Math.floor(instructor.rating) ? "#FCD34D" : "#E5E7EB"}
                />
              ))}
              <Text style={styles.ratingText}>{instructor.rating}</Text>
            </View>
          </View>
          <Pressable
            onPress={onViewProfile}
            style={({ pressed }) => [
              styles.moreButton,
              { opacity: pressed ? 0.7 : 1 },
            ]}
          >
            <Ionicons name="ellipsis-vertical" size={20} color="#6B7280" />
          </Pressable>
        </View>

        <View style={styles.stats}>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{instructor.lessonsCompleted}</Text>
            <Text style={styles.statLabel}>שיעורים</Text>
          </View>
          <View style={styles.statDivider} />
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{progress.hoursCompleted}</Text>
            <Text style={styles.statLabel}>שעות נהיגה</Text>
          </View>
          <View style={styles.statDivider} />
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{progress.daysUntilTest}</Text>
            <Text style={styles.statLabel}>ימים למבחן</Text>
          </View>
        </View>

        <Pressable
          style={({ pressed }) => [
            styles.contactBtn,
            { transform: [{ scale: pressed ? 0.98 : 1 }] },
          ]}
          onPress={onChat}
        >
          <LinearGradient
            colors={["#445EFB", "#6EA8FF"]}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
            style={styles.contactGradient}
          >
            <MaterialIcons name="chat" size={20} color="#fff" />
            <Text style={styles.contactText}>שלח הודעה למורה</Text>
          </LinearGradient>
        </Pressable>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  card: {
    backgroundColor: "#fff",
    borderRadius: 20,
    marginBottom: 16,
    overflow: "hidden",
    ...Platform.select({
      ios: {
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.08,
        shadowRadius: 8,
      },
      android: {
        elevation: 3,
      },
    }),
  },
  content: {
    padding: 20,
  },
  header: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginBottom: 20,
  },
  avatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: "#EFF6FF",
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  initial: {
    fontSize: 20,
    fontWeight: "700",
    color: "#3B82F6",
  },
  info: {
    flex: 1,
  },
  name: {
    fontSize: 18,
    fontWeight: "700",
    color: "#1F2937",
    marginBottom: 2,
  },
  role: {
    fontSize: 14,
    color: "#6B7280",
    marginBottom: 6,
  },
  rating: {
    flexDirection: "row",
    alignItems: "center",
    gap: 2,
  },
  ratingText: {
    fontSize: 12,
    fontWeight: "600",
    color: "#6B7280",
    marginLeft: 4,
  },
  moreButton: {
    padding: 4,
  },
  stats: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 16,
    borderTopWidth: 1,
    borderBottomWidth: 1,
    borderColor: "#F3F4F6",
    marginBottom: 16,
  },
  statItem: {
    flex: 1,
    alignItems: "center",
  },
  statValue: {
    fontSize: 20,
    fontWeight: "800",
    color: "#1F2937",
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: "#6B7280",
  },
  statDivider: {
    width: 1,
    height: 30,
    backgroundColor: "#E5E7EB",
  },
  contactBtn: {
    borderRadius: 12,
    overflow: "hidden",
  },
  contactGradient: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    gap: 8,
    paddingVertical: 14,
  },
  contactText: {
    fontSize: 16,
    fontWeight: "700",
    color: "#fff",
  },
});