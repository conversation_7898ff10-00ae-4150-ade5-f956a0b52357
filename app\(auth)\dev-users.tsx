import { Stack, useRouter } from "expo-router";
import React, { useEffect, useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  RefreshControl,
  TextInput,
  Pressable,
  StatusBar,
  ActivityIndicator,
  Alert,
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { Ionicons } from "@expo/vector-icons";
import { BlurView } from "expo-blur";
import * as Haptics from "expo-haptics";
import { DevAPI, APIError, UserListItem } from "../../services/api";

// Only show this screen in development
if (!__DEV__) {
  throw new Error('Dev users screen is only available in development mode');
}

export default function DevUsersScreen() {
  const router = useRouter();
  const insets = useSafeAreaInsets();
  
  const [users, setUsers] = useState<UserListItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [error, setError] = useState<string | null>(null);

  const loadUsers = async (isRefresh = false) => {
    try {
      if (isRefresh) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }
      setError(null);

      const response = await DevAPI.listUsers(searchQuery || undefined);
      setUsers(response.items);
    } catch (error) {
      console.error('Failed to load users:', error);
      let errorMessage = 'Failed to load users';
      
      if (error instanceof APIError) {
        errorMessage = error.message;
        if (error.status === 401) {
          errorMessage = 'Admin token invalid. Check backend configuration.';
        } else if (error.status === 0) {
          errorMessage = 'Cannot connect to backend. Is the server running?';
        }
      }
      
      setError(errorMessage);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const testHealthCheck = async () => {
    try {
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      const health = await DevAPI.checkHealth();
      Alert.alert('Health Check', `Server is ${health.status}\nDatabase: ${health.database}`, [
        { text: 'OK', style: 'default' }
      ]);
    } catch (error) {
      Alert.alert('Health Check Failed', error instanceof APIError ? error.message : 'Unknown error', [
        { text: 'OK', style: 'destructive' }
      ]);
    }
  };

  useEffect(() => {
    loadUsers();
  }, []);

  // Search with debounce
  useEffect(() => {
    const timer = setTimeout(() => {
      if (searchQuery.trim() !== "") {
        loadUsers();
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [searchQuery]);

  const renderUserItem = ({ item }: { item: UserListItem }) => (
    <View style={styles.userCard}>
      <BlurView intensity={20} tint="light" style={styles.userCardBlur}>
        <View style={styles.userCardContent}>
          <View style={styles.userHeader}>
            <Text style={styles.userName}>{item.fullName}</Text>
            <View style={styles.userIdBadge}>
              <Text style={styles.userIdText}>{item._id.slice(-8)}</Text>
            </View>
          </View>
          
          <View style={styles.userDetails}>
            <View style={styles.userDetailRow}>
              <Ionicons name="call" size={16} color="#6B7280" />
              <Text style={styles.userDetailText}>{item.phone}</Text>
            </View>
            
            {item.email && (
              <View style={styles.userDetailRow}>
                <Ionicons name="mail" size={16} color="#6B7280" />
                <Text style={styles.userDetailText}>{item.email}</Text>
              </View>
            )}
            
            {item.city && (
              <View style={styles.userDetailRow}>
                <Ionicons name="location" size={16} color="#6B7280" />
                <Text style={styles.userDetailText}>{item.city}</Text>
              </View>
            )}
            
            <View style={styles.userDetailRow}>
              <Ionicons name="time" size={16} color="#6B7280" />
              <Text style={styles.userDetailText}>
                {new Date(item.createdAt).toLocaleDateString('he-IL')} {new Date(item.createdAt).toLocaleTimeString('he-IL', { hour: '2-digit', minute: '2-digit' })}
              </Text>
            </View>
          </View>
        </View>
      </BlurView>
    </View>
  );

  return (
    <>
      <Stack.Screen options={{ headerShown: false }} />
      <StatusBar barStyle="dark-content" />
      
      <LinearGradient
        colors={["#EDF3FF", "#EAF1FF", "#E7EEFF"]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.container}
      >
        {/* Header */}
        <View style={[styles.header, { paddingTop: insets.top + 10 }]}>
          <Pressable
            onPress={() => {
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
              router.back();
            }}
            style={styles.backButton}
          >
            <BlurView intensity={30} tint="light" style={styles.backButtonBlur}>
              <Ionicons name="chevron-forward" size={24} color="#445EFB" />
            </BlurView>
          </Pressable>

          <View style={styles.titleSection}>
            <Text style={styles.title}>רשימת משתמשים</Text>
            <Text style={styles.subtitle}>מצב פיתוח בלבד</Text>
          </View>

          <Pressable onPress={testHealthCheck} style={styles.healthButton}>
            <BlurView intensity={30} tint="light" style={styles.healthButtonBlur}>
              <Ionicons name="pulse" size={20} color="#10B981" />
            </BlurView>
          </Pressable>
        </View>

        {/* Search */}
        <View style={styles.searchContainer}>
          <BlurView intensity={15} tint="light" style={styles.searchBlur}>
            <View style={styles.searchContent}>
              <Ionicons name="search" size={18} color="#9CA3AF" />
              <TextInput
                value={searchQuery}
                onChangeText={setSearchQuery}
                placeholder="חפש משתמש..."
                placeholderTextColor="#9CA3AF"
                style={styles.searchInput}
              />
              {searchQuery.length > 0 && (
                <Pressable onPress={() => setSearchQuery("")}>
                  <Ionicons name="close-circle" size={20} color="#9CA3AF" />
                </Pressable>
              )}
            </View>
          </BlurView>
        </View>

        {/* Content */}
        {error ? (
          <View style={styles.errorContainer}>
            <Ionicons name="alert-circle" size={48} color="#EF4444" />
            <Text style={styles.errorTitle}>Connection Error</Text>
            <Text style={styles.errorMessage}>{error}</Text>
            <Pressable
              onPress={() => loadUsers()}
              style={styles.retryButton}
            >
              <Text style={styles.retryButtonText}>Try Again</Text>
            </Pressable>
          </View>
        ) : loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#445EFB" />
            <Text style={styles.loadingText}>Loading users...</Text>
          </View>
        ) : (
          <FlatList
            data={users}
            keyExtractor={(item) => item._id}
            renderItem={renderUserItem}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={() => loadUsers(true)}
                tintColor="#445EFB"
              />
            }
            contentContainerStyle={[
              styles.listContent,
              { paddingBottom: insets.bottom + 20 }
            ]}
            showsVerticalScrollIndicator={false}
            ListEmptyComponent={
              <View style={styles.emptyContainer}>
                <Ionicons name="people-outline" size={64} color="#9CA3AF" />
                <Text style={styles.emptyTitle}>No Users Found</Text>
                <Text style={styles.emptyMessage}>
                  {searchQuery ? "No users match your search" : "No users have registered yet"}
                </Text>
              </View>
            }
          />
        )}
      </LinearGradient>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  
  // Header
  header: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 22,
    paddingBottom: 20,
  },
  backButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    overflow: "hidden",
  },
  backButtonBlur: {
    width: "100%",
    height: "100%",
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(255, 255, 255, 0.75)",
    borderWidth: StyleSheet.hairlineWidth,
    borderColor: "rgba(0, 0, 0, 0.08)",
  },
  titleSection: {
    flex: 1,
    alignItems: "center",
  },
  title: {
    fontSize: 20,
    fontWeight: "800",
    color: "#0B0B0B",
  },
  subtitle: {
    fontSize: 12,
    color: "rgba(0, 0, 0, 0.5)",
    fontWeight: "600",
  },
  healthButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    overflow: "hidden",
  },
  healthButtonBlur: {
    width: "100%",
    height: "100%",
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(255, 255, 255, 0.75)",
    borderWidth: StyleSheet.hairlineWidth,
    borderColor: "rgba(0, 0, 0, 0.08)",
  },

  // Search
  searchContainer: {
    paddingHorizontal: 22,
    marginBottom: 20,
  },
  searchBlur: {
    borderRadius: 16,
    overflow: "hidden",
    backgroundColor: "rgba(255, 255, 255, 0.5)",
  },
  searchContent: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 12,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: "#0B0B0B",
    textAlign: "right",
  },

  // List
  listContent: {
    paddingHorizontal: 22,
  },
  userCard: {
    marginBottom: 12,
    borderRadius: 16,
    overflow: "hidden",
  },
  userCardBlur: {
    backgroundColor: "rgba(255, 255, 255, 0.7)",
    borderWidth: 1,
    borderColor: "rgba(0, 0, 0, 0.06)",
  },
  userCardContent: {
    padding: 16,
  },
  userHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  userName: {
    fontSize: 18,
    fontWeight: "700",
    color: "#0B0B0B",
    flex: 1,
  },
  userIdBadge: {
    backgroundColor: "rgba(68, 94, 251, 0.1)",
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  userIdText: {
    fontSize: 10,
    fontWeight: "700",
    color: "#445EFB",
    fontFamily: "monospace",
  },
  userDetails: {
    gap: 8,
  },
  userDetailRow: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  userDetailText: {
    fontSize: 14,
    color: "#6B7280",
    flex: 1,
    textAlign: "right",
  },

  // States
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    gap: 16,
  },
  loadingText: {
    fontSize: 16,
    color: "#6B7280",
    fontWeight: "600",
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 32,
    gap: 16,
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: "700",
    color: "#0B0B0B",
    textAlign: "center",
  },
  errorMessage: {
    fontSize: 14,
    color: "#6B7280",
    textAlign: "center",
    lineHeight: 20,
  },
  retryButton: {
    backgroundColor: "#445EFB",
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 12,
    marginTop: 8,
  },
  retryButtonText: {
    color: "#fff",
    fontWeight: "700",
    fontSize: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingTop: 100,
    gap: 16,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: "700",
    color: "#0B0B0B",
  },
  emptyMessage: {
    fontSize: 14,
    color: "#6B7280",
    textAlign: "center",
  },
});