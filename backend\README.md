# DLSA Backend - Registration API

A professional Node.js backend service for the DLSA Driving Instructor App that handles user registration with MongoDB storage.

## Features

- **User Registration**: Two-step registration process (personal details + address)
- **Phone Validation**: E.164 phone number normalization and validation
- **Hebrew Support**: Full Hebrew text support for names and addresses
- **Security**: Rate limiting, CORS, helmet protection
- **Database**: MongoDB with proper indexing and duplicate prevention
- **Validation**: Comprehensive Zod schema validation
- **Logging**: Structured logging with Pino
- **Dev Tools**: Admin API for user listing (development only)
- **TypeScript**: Full TypeScript implementation with proper types
- **Error Handling**: Professional error responses and logging

## API Endpoints

### Registration Flow

#### 1. Start Registration
```http
POST /api/registration/start
Content-Type: application/json

{
  "fullName": "ישראל ישראלי",
  "phone": "+************",
  "email": "<EMAIL>",
  "deviceId": "uuid-v4"
}
```

**Response (201):**
```json
{
  "regId": "c2d0c469-7e8f-49a8-92f8-1a2cb28b3a5f",
  "status": "PENDING"
}
```

#### 2. Complete Registration
```http
PATCH /api/registration/:regId/complete
Content-Type: application/json

{
  "address": {
    "city": "לוד",
    "street": "הרצל",
    "buildingNumber": "12",
    "apartment": "5"
  }
}
```

**Response (200):**
```json
{
  "status": "COMPLETE",
  "userId": "c2d0c469-...",
  "createdAt": "2025-08-18T21:00:00Z"
}
```

#### 3. Get Registration Status
```http
GET /api/registration/:regId
```

### Development API

#### List Users (Dev Only)
```http
GET /api/users?q=search&limit=20
X-Admin-Token: dev-only-secret-12345
```

#### Health Check
```http
GET /healthz
```

## Setup Instructions

### 1. MongoDB Setup

**Option A: Local MongoDB**
```bash
# Install MongoDB locally
# Default connection: mongodb://localhost:27017
```

**Option B: MongoDB Atlas**
```bash
# Create free cluster at https://cloud.mongodb.com
# Get connection string like: mongodb+srv://user:<EMAIL>
```

### 2. Environment Configuration

Update `.env` file:
```bash
# Database
MONGODB_URI=mongodb://localhost:27018/DLSA_app  # Update with your MongoDB URI
MONGODB_DB=DLSA_app

# Server
PORT=3000
NODE_ENV=development

# Security
DEV_ADMIN_TOKEN=dev-only-secret-12345  # Used for dev API access

# Logging
LOG_LEVEL=info
```

### 3. Install and Run

```bash
# Install dependencies
npm install

# Build TypeScript
npm run build

# Start development server (with auto-reload)
npm run dev

# Or start production server
npm start
```

### 4. Verification

Server should start and display:
```
🚀 Server running on http://0.0.0.0:3000
📊 Health check: http://0.0.0.0:3000/healthz
📋 Environment: development
Connected to MongoDB database: DLSA_app
Database indexes created successfully
```

## Testing

### Health Check
```bash
curl http://localhost:3000/healthz
```

### Test Registration Flow
```bash
# 1. Start registration
curl -X POST http://localhost:3000/api/registration/start \
  -H "Content-Type: application/json" \
  -d '{
    "fullName": "משה כהן",
    "phone": "**********",
    "email": "<EMAIL>",
    "deviceId": "123e4567-e89b-12d3-a456-426614174000"
  }'

# Save the regId from response, then:

# 2. Complete registration
curl -X PATCH http://localhost:3000/api/registration/YOUR_REG_ID/complete \
  -H "Content-Type: application/json" \
  -d '{
    "address": {
      "city": "תל אביב",
      "street": "דיזנגוף",
      "buildingNumber": "50",
      "apartment": "10"
    }
  }'

# 3. List users (dev only)
curl -H "X-Admin-Token: dev-only-secret-12345" \
  http://localhost:3000/api/users
```

## Database Schema

### Users Collection
```typescript
{
  _id: string,            // regId (UUID v4)
  fullName: string,       // Hebrew name supported
  phone: string,          // E.164 format (+972...)
  email?: string,         // Optional
  address?: {
    city: string,
    street: string,
    buildingNumber: string,
    apartment?: string
  },
  status: 'PENDING' | 'COMPLETE',
  regStage: 1 | 2,        // 1 after start, 2 after complete
  deviceId: string,       // From client
  createdAt: Date,
  updatedAt: Date
}
```

### Indexes
- `phone` - Unique index (prevents duplicate registrations)
- `createdAt` - Descending (for chronological listing)
- Text search on `fullName`, `email`, `address.city`

## Error Handling

The API returns structured errors:

```json
{
  "error": "Phone number already registered",
  "code": "DUPLICATE_PHONE"
}
```

Common error codes:
- `DUPLICATE_PHONE` - Phone number already registered
- `VALIDATION_ERROR` - Invalid input data
- `NOT_FOUND` - Registration ID not found
- `UNAUTHORIZED` - Invalid admin token (dev API)

## Security Features

- **Rate Limiting**: 100 requests/minute globally, 10 requests/minute for registration
- **CORS**: Configured for development and production origins
- **Helmet**: Security headers
- **Input Validation**: Comprehensive Zod schemas
- **Phone Normalization**: E.164 format enforcement
- **No PII Logging**: Sensitive data excluded from logs

## Development

### Code Quality
```bash
npm run lint     # ESLint checking
npm run format   # Prettier formatting
npm run build    # TypeScript compilation
```

### File Structure
```
backend/
├── src/
│   ├── server.ts      # Main server and routes
│   ├── database.ts    # MongoDB connection and setup
│   ├── schemas.ts     # Zod validation schemas
│   └── types.ts       # TypeScript type definitions
├── dist/              # Compiled JavaScript
├── .env               # Environment configuration
├── package.json       # Dependencies and scripts
└── tsconfig.json      # TypeScript configuration
```

## Production Deployment

### Environment Variables
Set these in production:
```bash
MONGODB_URI=mongodb+srv://user:<EMAIL>/drivio
NODE_ENV=production
PORT=3000
# Remove DEV_ADMIN_TOKEN in production
```

### Deployment Platforms
- **Render**: Auto-deploy from Git
- **Fly.io**: Docker-based deployment  
- **Railway**: Simple database + app hosting
- **Vercel**: Serverless functions

### Security Checklist
- [ ] Update MongoDB URI for production
- [ ] Remove or secure DEV_ADMIN_TOKEN
- [ ] Configure CORS for production domains
- [ ] Enable MongoDB backups
- [ ] Set up monitoring/alerting
- [ ] Use HTTPS in production

## Troubleshooting

### Connection Issues
- Verify MongoDB is running
- Check MONGODB_URI format
- Ensure database permissions
- Check network connectivity

### Port Issues
- Default port 3000 may be in use
- Change PORT in .env
- Use `lsof -i :3000` to check port usage

### Validation Errors
- Phone numbers must be Israeli format (0xx-xxx-xxxx or +972)
- Hebrew characters supported in names and addresses
- Email is optional but must be valid format if provided

## Support

For issues or questions:
1. Check logs in console output
2. Verify environment configuration
3. Test endpoints with curl or Postman
4. Review MongoDB connection and data