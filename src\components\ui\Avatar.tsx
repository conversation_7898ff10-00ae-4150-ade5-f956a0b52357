import React from 'react';
import { View, Text, StyleSheet, Image, ViewStyle, Pressable } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import * as Haptics from 'expo-haptics';

interface AvatarProps {
  source?: { uri: string } | number;
  name?: string;
  size?: 'small' | 'medium' | 'large' | 'xlarge';
  variant?: 'circle' | 'rounded';
  gradient?: boolean;
  onPress?: () => void;
  badge?: boolean | number | 'online' | 'offline';
  style?: ViewStyle;
}

export const Avatar: React.FC<AvatarProps> = ({
  source,
  name,
  size = 'medium',
  variant = 'circle',
  gradient = false,
  onPress,
  badge,
  style,
}) => {
  const initial = name ? name.charAt(0).toUpperCase() : '?';
  
  const handlePress = async () => {
    if (onPress) {
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      onPress();
    }
  };

  const renderAvatar = () => {
    if (source) {
      return (
        <View style={[
          styles.container,
          sizeStyles[size],
          variant === 'rounded' && styles.rounded,
          style,
        ]}>
          <Image source={source} style={styles.image} resizeMode="cover" />
        </View>
      );
    }

    if (gradient) {
      return (
        <LinearGradient
          colors={['#445EFB', '#6EA8FF']}
          style={[
            styles.container,
            sizeStyles[size],
            variant === 'rounded' && styles.rounded,
            style,
          ]}
        >
          <Text style={[styles.initialWhite, sizeStyles[size].text]}>
            {initial}
          </Text>
        </LinearGradient>
      );
    }

    return (
      <View style={[
        styles.container,
        styles.defaultBg,
        sizeStyles[size],
        variant === 'rounded' && styles.rounded,
        style,
      ]}>
        <Text style={[styles.initial, sizeStyles[size].text]}>
          {initial}
        </Text>
      </View>
    );
  };

  const renderBadge = () => {
    if (!badge) return null;

    if (badge === 'online') {
      return (
        <View style={[styles.badge, styles.onlineBadge, sizeStyles[size].badge]} />
      );
    }

    if (badge === 'offline') {
      return (
        <View style={[styles.badge, styles.offlineBadge, sizeStyles[size].badge]} />
      );
    }

    if (typeof badge === 'number') {
      return (
        <View style={[styles.badge, styles.numberBadge, sizeStyles[size].badge]}>
          <Text style={styles.badgeText}>{badge > 99 ? '99+' : badge}</Text>
        </View>
      );
    }

    return (
      <View style={[styles.badge, styles.dotBadge, sizeStyles[size].badge]} />
    );
  };

  if (onPress) {
    return (
      <Pressable onPress={handlePress} style={styles.pressable}>
        {renderAvatar()}
        {renderBadge()}
      </Pressable>
    );
  }

  return (
    <View style={styles.wrapper}>
      {renderAvatar()}
      {renderBadge()}
    </View>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    position: 'relative',
  },
  pressable: {
    position: 'relative',
  },
  container: {
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
  },
  defaultBg: {
    backgroundColor: '#EFF6FF',
  },
  rounded: {
    borderRadius: 16,
  },
  image: {
    width: '100%',
    height: '100%',
  },
  initial: {
    fontWeight: '700',
    color: '#3B82F6',
  },
  initialWhite: {
    fontWeight: '700',
    color: '#FFFFFF',
  },
  badge: {
    position: 'absolute',
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
  onlineBadge: {
    backgroundColor: '#10B981',
  },
  offlineBadge: {
    backgroundColor: '#6B7280',
  },
  dotBadge: {
    backgroundColor: '#EF4444',
  },
  numberBadge: {
    backgroundColor: '#EF4444',
    minWidth: 20,
    paddingHorizontal: 4,
    justifyContent: 'center',
    alignItems: 'center',
  },
  badgeText: {
    fontSize: 10,
    fontWeight: '700',
    color: '#FFFFFF',
  },
});

const sizeStyles = {
  small: {
    width: 32,
    height: 32,
    borderRadius: 16,
    text: { fontSize: 14 },
    badge: {
      width: 10,
      height: 10,
      borderRadius: 5,
      bottom: 0,
      right: 0,
    },
  },
  medium: {
    width: 48,
    height: 48,
    borderRadius: 24,
    text: { fontSize: 20 },
    badge: {
      width: 14,
      height: 14,
      borderRadius: 7,
      bottom: 0,
      right: 0,
    },
  },
  large: {
    width: 64,
    height: 64,
    borderRadius: 32,
    text: { fontSize: 28 },
    badge: {
      width: 18,
      height: 18,
      borderRadius: 9,
      bottom: 2,
      right: 2,
    },
  },
  xlarge: {
    width: 80,
    height: 80,
    borderRadius: 40,
    text: { fontSize: 36 },
    badge: {
      width: 22,
      height: 22,
      borderRadius: 11,
      bottom: 4,
      right: 4,
    },
  },
};