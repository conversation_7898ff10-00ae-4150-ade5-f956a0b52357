import { useRouter } from "expo-router";
import { useEffect } from "react";
import { View, ActivityIndicator } from "react-native";

export default function Index() {
  const router = useRouter();

  useEffect(() => {
    // Redirect to welcome screen on app launch
    setTimeout(() => {
      router.replace("/(auth)/welcome");
    }, 100);
  }, []);

  return (
    <View style={{ flex: 1, justifyContent: "center", alignItems: "center", backgroundColor: "#445EFB" }}>
      <ActivityIndicator size="large" color="#fff" />
    </View>
  );
  
}
