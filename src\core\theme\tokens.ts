// src/core/theme/tokens.ts
export const tokens = {
  colors: {
    // Primary palette
    primary: '#445EFB',
    primaryLight: '#6EA8FF',
    primaryDark: '#2E47D9',
    
    // Backgrounds
    backgroundPure: '#FFFFFF',
    backgroundSurface: '#F8F9FA',
    backgroundElevated: '#FFFFFF',
    
    // Text colors
    textPrimary: '#1A1A1A',
    textSecondary: '#6B7280',
    textTertiary: '#9CA3AF',
    textWhite: '#FFFFFF',
    
    // Status colors
    success: '#10B981',
    successBackground: '#ECFDF5',
    warning: '#F59E0B',
    warningBackground: '#FFFBEB',
    error: '#EF4444',
    errorBackground: '#FEE2E2',
    info: '#3B82F6',
    infoBackground: '#EFF6FF',
    
    // Borders
    border: '#E5E7EB',
    borderFocused: '#445EFB',
    borderError: '#EF4444',
    
    // Special
    surfaceGlass: 'rgba(255, 255, 255, 0.7)',
  },
  
  typography: {
    // Font sizes
    xs: 11,
    sm: 13,
    base: 15,
    lg: 17,
    xl: 20,
    '2xl': 24,
    '3xl': 28,
    '4xl': 34,
    
    // Font weights
    regular: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
    heavy: '800',
    
    // Line heights
    tight: 1.2,
    normal: 1.5,
    relaxed: 1.75,
  },
  
  spacing: {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
    '2xl': 48,
    '3xl': 64,
  },
  
  borderRadius: {
    sm: 8,
    md: 12,
    lg: 16,
    xl: 20,
    full: 999,
  },
  
  shadows: {
    sm: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.05,
      shadowRadius: 2,
      elevation: 2,
    },
    md: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.08,
      shadowRadius: 8,
      elevation: 4,
    },
    lg: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.12,
      shadowRadius: 16,
      elevation: 8,
    },
    primary: {
      shadowColor: '#445EFB',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.25,
      shadowRadius: 12,
      elevation: 8,
    },
  },
  
  animation: {
    fast: 200,
    normal: 300,
    slow: 500,
    verySlow: 800,
  },
};