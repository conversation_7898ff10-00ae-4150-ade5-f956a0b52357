// src/features/dashboard/components/DashboardHeader.tsx
import React, { useEffect, useRef } from "react";
import {
  View,
  Text,
  Animated,
  Pressable,
  StyleSheet,
  Platform,
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { BlurView } from "expo-blur";
import { Ionicons } from "@expo/vector-icons";
import { EdgeInsets } from "react-native-safe-area-context";

const HEADER_MAX_HEIGHT = 180;
const HEADER_MIN_HEIGHT = 56;
const HEADER_SCROLL_DISTANCE = HEADER_MAX_HEIGHT - HEADER_MIN_HEIGHT;
const COLLAPSE_THRESHOLD = HEADER_SCROLL_DISTANCE * 0.5;
const SLOW_ANIMATION_MULTIPLIER = 1.8;

interface Props {
  insets: EdgeInsets;
  scrollY: Animated.Value;
  user: any;
  greeting: string;
  progress: {
    totalLessons: number;
    completedLessons: number;
  };
  onNotifications: () => void;
  onProfile: () => void;
}

export function DashboardHeader({
  insets,
  scrollY,
  user,
  greeting,
  progress,
  onNotifications,
  onProfile,
}: Props) {
  const headerScale = useRef(new Animated.Value(0.9)).current;
  const headerOpacity = useRef(new Animated.Value(0)).current;
  const blob1Translate = useRef(new Animated.Value(0)).current;
  const blob2Translate = useRef(new Animated.Value(0)).current;
  const progressRing = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Header entrance animation
    Animated.parallel([
      Animated.spring(headerScale, {
        toValue: 1,
        tension: 30,
        friction: 8,
        useNativeDriver: true,
      }),
      Animated.timing(headerOpacity, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
    ]).start();

    // Progress ring animation
    Animated.timing(progressRing, {
      toValue: progress.completedLessons / progress.totalLessons,
      duration: 1500,
      delay: 800,
      useNativeDriver: false,
    }).start();

    // Floating blob animations
    const blob1Animation = Animated.loop(
      Animated.sequence([
        Animated.timing(blob1Translate, {
          toValue: 20,
          duration: 3500,
          useNativeDriver: true,
        }),
        Animated.timing(blob1Translate, {
          toValue: -20,
          duration: 3500,
          useNativeDriver: true,
        }),
      ])
    );

    const blob2Animation = Animated.loop(
      Animated.sequence([
        Animated.timing(blob2Translate, {
          toValue: -15,
          duration: 4000,
          useNativeDriver: true,
        }),
        Animated.timing(blob2Translate, {
          toValue: 15,
          duration: 4000,
          useNativeDriver: true,
        }),
      ])
    );

    blob1Animation.start();
    blob2Animation.start();

    return () => {
      blob1Animation.stop();
      blob2Animation.stop();
    };
  }, []);

  const navTranslateY = scrollY.interpolate({
    inputRange: [0, COLLAPSE_THRESHOLD * SLOW_ANIMATION_MULTIPLIER],
    outputRange: [0, -14],
    extrapolate: 'clamp',
  });

  const nameFontSize = scrollY.interpolate({
    inputRange: [0, COLLAPSE_THRESHOLD * SLOW_ANIMATION_MULTIPLIER],
    outputRange: [28, 22],
    extrapolate: 'clamp',
  });

  return (
    <Animated.View 
      style={[
        styles.headerWrapper,
        {
          height: scrollY.interpolate({
            inputRange: [0, HEADER_SCROLL_DISTANCE * SLOW_ANIMATION_MULTIPLIER],
            outputRange: [HEADER_MAX_HEIGHT + insets.top, HEADER_MIN_HEIGHT + insets.top],
            extrapolate: 'clamp',
          }),
        },
      ]}
    >
      <View style={styles.headerBackground}>
        <LinearGradient
          colors={["#6B7FFF", "#5E71F8", "#5568FE"]}
          style={[styles.headerGradient, { paddingTop: insets.top }]}
        >
          {/* Floating blobs */}
          <Animated.View 
            style={[
              styles.blobs,
              {
                opacity: scrollY.interpolate({
                  inputRange: [0, HEADER_SCROLL_DISTANCE * 0.6 * SLOW_ANIMATION_MULTIPLIER],
                  outputRange: [0.6, 0],
                  extrapolate: 'clamp',
                }),
              },
            ]} 
            pointerEvents="none"
          >
            <Animated.View
              style={[
                styles.blob,
                styles.blobA,
                {
                  transform: [
                    { translateX: blob1Translate },
                    { translateY: Animated.multiply(blob1Translate, 0.3) },
                  ],
                },
              ]}
            />
            <Animated.View
              style={[
                styles.blob,
                styles.blobB,
                {
                  transform: [
                    { translateX: blob2Translate },
                    { translateY: Animated.multiply(blob2Translate, -0.5) },
                  ],
                },
              ]}
            />
          </Animated.View>

          <View style={styles.headerContent}>
            {/* Navigation Bar Section */}
            <Animated.View style={[styles.navSection, { paddingTop: 6, transform: [{ translateY: navTranslateY }] }]}>
              <View style={styles.userSection}>
                <Animated.Text 
                  style={[
                    styles.greeting,
                    {
                      opacity: scrollY.interpolate({
                        inputRange: [0, COLLAPSE_THRESHOLD * 0.8 * SLOW_ANIMATION_MULTIPLIER],
                        outputRange: [1, 0],
                        extrapolate: 'clamp',
                      }),
                    },
                  ]}
                >
                  {greeting},
                </Animated.Text>
                <Animated.Text
                  style={[
                    styles.userName,
                    { fontSize: nameFontSize, lineHeight: Animated.add(nameFontSize, 2) },
                  ]}
                >
                  {user?.fullName || "טוען..."}
                </Animated.Text>
              </View>

              {/* Action Buttons */}
              <View style={styles.actionsSection}>
                <Pressable
                  onPress={onNotifications}
                  style={({ pressed }) => [
                    styles.iconButton,
                    { transform: [{ scale: pressed ? 0.9 : 1 }] },
                  ]}
                >
                  <BlurView intensity={20} tint="light" style={styles.iconButtonBlur}>
                    <Ionicons name="notifications" size={22} color="#fff" />
                    <View style={styles.notificationBadge}>
                      <Text style={styles.notificationBadgeText}>2</Text>
                    </View>
                  </BlurView>
                </Pressable>
                <Pressable
                  onPress={onProfile}
                  style={({ pressed }) => [
                    styles.iconButton,
                    { transform: [{ scale: pressed ? 0.9 : 1 }] },
                  ]}
                >
                  <BlurView intensity={20} tint="light" style={styles.iconButtonBlur}>
                    <Ionicons name="person" size={22} color="#fff" />
                  </BlurView>
                </Pressable>
              </View>
            </Animated.View>

            {/* Progress Summary */}
            <Animated.View 
              style={[
                styles.progressSection,
                {
                  opacity: scrollY.interpolate({
                    inputRange: [0, COLLAPSE_THRESHOLD * 0.9 * SLOW_ANIMATION_MULTIPLIER, COLLAPSE_THRESHOLD * SLOW_ANIMATION_MULTIPLIER],
                    outputRange: [1, 0.3, 0],
                    extrapolate: 'clamp',
                  }),
                  transform: [
                    {
                      translateY: scrollY.interpolate({
                        inputRange: [0, COLLAPSE_THRESHOLD * SLOW_ANIMATION_MULTIPLIER],
                        outputRange: [0, -25],
                        extrapolate: 'clamp',
                      }),
                    },
                    {
                      scale: scrollY.interpolate({
                        inputRange: [0, COLLAPSE_THRESHOLD * SLOW_ANIMATION_MULTIPLIER],
                        outputRange: [1, 0.8],
                        extrapolate: 'clamp',
                      }),
                    },
                  ],
                },
              ]}
            >
              <View style={styles.progressCircle}>
                <Animated.View
                  style={[
                    styles.progressRing,
                    {
                      transform: [
                        {
                          rotate: progressRing.interpolate({
                            inputRange: [0, 1],
                            outputRange: ["0deg", "360deg"],
                          }),
                        },
                      ],
                    },
                  ]}
                />
                <Text style={styles.progressPercent}>
                  {Math.round((progress.completedLessons / progress.totalLessons) * 100)}%
                </Text>
              </View>
              <View style={styles.progressDetails}>
                <Text style={styles.progressTitle}>התקדמות לקראת המבחן</Text>
                <Text style={styles.progressSubtitle}>
                  {progress.completedLessons} מתוך {progress.totalLessons} שיעורים
                </Text>
                <View style={styles.progressBar}>
                  <Animated.View
                    style={[
                      styles.progressFill,
                      {
                        width: progressRing.interpolate({
                          inputRange: [0, 1],
                          outputRange: ["0%", "100%"],
                        }),
                      },
                    ]}
                  />
                </View>
              </View>
            </Animated.View>
          </View>
        </LinearGradient>
      </View>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  headerWrapper: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 10,
    overflow: 'hidden',
  },
  headerBackground: {
    flex: 1,
    borderBottomLeftRadius: 32,
    borderBottomRightRadius: 32,
    overflow: 'hidden',
    ...Platform.select({
      ios: {
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 6 },
        shadowRadius: 16,
        shadowOpacity: 0.1,
      },
      android: {
        elevation: 12,
      },
    }),
  },
  headerGradient: {
    flex: 1,
    paddingHorizontal: 24,
    paddingBottom: 24,
  },
  headerContent: {
    flex: 1,
    justifyContent: 'space-between',
  },
  navSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    minHeight: 44,
  },
  userSection: {
    flex: 1,
  },
  actionsSection: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 12,
  },
  progressSection: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 20,
    paddingTop: 20,
  },
  blobs: {
    position: "absolute",
    width: "100%",
    height: "100%",
  },
  blob: {
    position: "absolute",
    borderRadius: 999,
    backgroundColor: "rgba(255, 255, 255, 0.1)",
  },
  blobA: {
    top: -50,
    right: -50,
    width: 150,
    height: 150,
  },
  blobB: {
    bottom: -30,
    left: -30,
    width: 120,
    height: 120,
  },
  greeting: {
    fontSize: 15,
    fontWeight: '600',
    color: "rgba(255, 255, 255, 0.85)",
    marginBottom: 2,
    letterSpacing: 0.2,
  },
  userName: {
    fontSize: 28,
    fontWeight: "900",
    color: "#fff",
    letterSpacing: -0.5,
    lineHeight: 32,
  },
  iconButton: {
    width: 42,
    height: 42,
    borderRadius: 21,
    overflow: "hidden",
    ...Platform.select({
      ios: {
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  iconButtonBlur: {
    width: "100%",
    height: "100%",
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(255, 255, 255, 0.18)",
  },
  notificationBadge: {
    position: "absolute",
    top: 8,
    right: 8,
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: "#EF4444",
    justifyContent: "center",
    alignItems: "center",
  },
  notificationBadgeText: {
    fontSize: 10,
    fontWeight: "700",
    color: "#fff",
  },
  progressCircle: {
    width: 85,
    height: 85,
    borderRadius: 42.5,
    backgroundColor: "rgba(255, 255, 255, 0.22)",
    justifyContent: "center",
    alignItems: "center",
  },
  progressRing: {
    position: "absolute",
    width: 81,
    height: 81,
    borderRadius: 40.5,
    borderWidth: 4.5,
    borderColor: "rgba(255, 255, 255, 0.95)",
    borderTopColor: "transparent",
    borderRightColor: "transparent",
  },
  progressPercent: {
    fontSize: 22,
    fontWeight: "900",
    color: "#fff",
    letterSpacing: -0.5,
  },
  progressDetails: {
    flex: 1,
  },
  progressTitle: {
    fontSize: 17,
    fontWeight: "800",
    color: "#fff",
    marginBottom: 3,
    letterSpacing: -0.2,
  },
  progressSubtitle: {
    fontSize: 13,
    fontWeight: '600',
    color: "rgba(255, 255, 255, 0.82)",
    marginBottom: 10,
    letterSpacing: 0.1,
  },
  progressBar: {
    height: 6,
    backgroundColor: "rgba(255, 255, 255, 0.25)",
    borderRadius: 3,
    overflow: "hidden",
  },
  progressFill: {
    height: "100%",
    backgroundColor: "rgba(255, 255, 255, 0.95)",
    borderRadius: 3,
  },
});