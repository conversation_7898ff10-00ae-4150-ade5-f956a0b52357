import React, { useMemo, useRef, useState } from 'react';
import { View, Text, StyleSheet, Pressable } from 'react-native';
import {
  BottomSheet,
  BottomSheetBackdrop,
  BottomSheetFlatList,
  BottomSheetTextInput,
} from '@gorhom/bottom-sheet';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Button } from '@/src/components';
import { tokens } from '@/src/core/theme/tokens';
import { SavedLocation } from '@/src/core/types/location.types';
import { LocationCard } from './LocationCard';

type Props = {
  selectedLocation: SavedLocation | null;
  savedLocations: SavedLocation[];
  onSelect: (loc: SavedLocation) => void;
  onConfirm: () => void;
  onUseCurrent: () => void;
  onOpenAddLocation: () => void;
};

export const LocationBottomSheet: React.FC<Props> = ({
  selectedLocation,
  savedLocations,
  onSelect,
  onConfirm,
  onUseCurrent,
  onOpenAddLocation,
}) => {
  const insets = useSafeAreaInsets();
  const sheetRef = useRef<BottomSheet>(null);

  // Snap like Instagram: closed bar / half / almost-full
  const CLOSED = 130; // px
  const snapPoints = useMemo(() => [CLOSED, '45%', '88%'], []);
  const [index, setIndex] = useState(1); // start at peek

  const backdrop = (props: any) => (
    <BottomSheetBackdrop
      {...props}
      appearsOnIndex={1}     // show when not closed
      disappearsOnIndex={0}
      pressBehavior="collapse" // tap to go to peek
    />
  );

  return (
    <BottomSheet
      ref={sheetRef}
      index={1}
      snapPoints={snapPoints}
      onChange={setIndex}
      backdropComponent={backdrop}
      enablePanDownToClose={false}
      keyboardBehavior="interactive"
      handleIndicatorStyle={{ backgroundColor: '#D1D5DB' }}
      style={styles.sheet}
      backgroundStyle={styles.sheetBg}
    >
      {/* CLOSED BAR (index 0) */}
      {index === 0 ? (
        <View style={styles.closedBar}>
          <Ionicons name="location" size={20} color={tokens.colors.primary} />
          <Text style={styles.closedText} numberOfLines={1}>
            {selectedLocation?.address || 'בחר מיקום'}
          </Text>
          <Button
            variant="primary"
            size="small"
            onPress={onConfirm}
            disabled={!selectedLocation}
          >
            אישור
          </Button>
        </View>
      ) : (
        // EXPANDED CONTENT (index 1/2)
        <View style={styles.expanded}>
          {/* Use my current location */}
          <Pressable
            onPress={onUseCurrent}
            style={({ pressed }) => [styles.currentBtn, pressed && styles.pressed]}
          >
            <MaterialIcons name="my-location" size={20} color={tokens.colors.success} />
            <Text style={styles.currentText}>השתמש במיקום הנוכחי</Text>
          </Pressable>

          {/* Search (keyboard-aware) */}
          <View style={styles.search}>
            <Ionicons name="search" size={20} color={tokens.colors.textTertiary} />
            <BottomSheetTextInput
              style={styles.searchInput}
              placeholder="חפש כתובת…"
              placeholderTextColor={tokens.colors.textTertiary}
              returnKeyType="search"
            />
          </View>

          {/* Header + Add */}
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>מיקומים שמורים</Text>
            <Pressable
              onPress={onOpenAddLocation}
              style={({ pressed }) => [styles.iconBtn, pressed && styles.pressed]}
            >
              <Ionicons name="add-circle" size={24} color={tokens.colors.primary} />
            </Pressable>
          </View>

          {/* List (gesture-integrated) */}
          <BottomSheetFlatList
            data={savedLocations}
            keyExtractor={(item) => item.id}
            contentContainerStyle={{ paddingHorizontal: 20, paddingBottom: 120 }}
            showsVerticalScrollIndicator={false}
            renderItem={({ item, index }) => (
              <LocationCard
                location={item}
                isSelected={selectedLocation?.id === item.id}
                onPress={() => onSelect(item)}
                index={index}
              />
            )}
          />

          {/* Sticky confirm */}
          <View style={[styles.confirmWrap, { paddingBottom: insets.bottom + 12 }]}>
            <Button
              variant="primary"
              size="large"
              fullWidth
              onPress={onConfirm}
              disabled={!selectedLocation}
            >
              אישור מיקום
            </Button>
          </View>
        </View>
      )}
    </BottomSheet>
  );
};

const styles = StyleSheet.create({
  sheet: {
    position: 'absolute',
    left: 0,
    right: 0,
  },
  sheetBg: {
    backgroundColor: tokens.colors.backgroundPure,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
  },

  // closed
  closedBar: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  closedText: {
    flex: 1,
    fontSize: 15,
    fontWeight: '500',
    color: tokens.colors.textPrimary,
  },

  // expanded
  expanded: { flex: 1 },
  currentBtn: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
    backgroundColor: tokens.colors.successBackground,
    marginHorizontal: 20,
    marginTop: 8,
    marginBottom: 12,
    paddingVertical: 14,
    borderRadius: tokens.borderRadius.lg,
  },
  currentText: {
    fontSize: 16,
    fontWeight: '600',
    color: tokens.colors.success,
  },
  search: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    backgroundColor: tokens.colors.backgroundSurface,
    marginHorizontal: 20,
    marginBottom: 16,
    paddingHorizontal: 16,
    height: 44,
    borderRadius: tokens.borderRadius.md,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: tokens.colors.textPrimary,
  },
  sectionHeader: {
    paddingHorizontal: 20,
    marginBottom: 10,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  sectionTitle: {
    fontSize: 13,
    fontWeight: '600',
    color: tokens.colors.textSecondary,
    letterSpacing: 0.5,
  },
  iconBtn: { padding: 4 },
  confirmWrap: {
    position: 'absolute',
    left: 0, right: 0, bottom: 0,
    paddingHorizontal: 20,
    paddingTop: 12,
    backgroundColor: tokens.colors.backgroundPure,
    borderTopWidth: StyleSheet.hairlineWidth,
    borderTopColor: tokens.colors.border,
  },
  pressed: { opacity: 0.6 },
});
