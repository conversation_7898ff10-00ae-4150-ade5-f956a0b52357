// src/features/profile/components/MoreOptionsList.tsx
import React from 'react';
import { StyleSheet } from 'react-native';
import { Card, ListItem, Divider } from '@/src/components';

interface Props {
  onSupport: () => void;
  onTerms: () => void;
  onPrivacy: () => void;
  onAbout: () => void;
}

export function MoreOptionsList({ onSupport, onTerms, onPrivacy, onAbout }: Props) {
  return (
    <Card variant="default" style={styles.container}>
      <ListItem
        title="עזרה ותמיכה"
        leftIcon="help-circle-outline"
        onPress={onSupport}
      />
      <Divider spacing="small" />
      <ListItem
        title="תנאי שימוש"
        leftIcon="document-text-outline"
        onPress={onTerms}
      />
      <Divider spacing="small" />
      <ListItem
        title="מדיניות פרטיות"
        leftIcon="shield-checkmark-outline"
        onPress={onPrivacy}
      />
      <Divider spacing="small" />
      <ListItem
        title="אודות"
        leftIcon="information-circle-outline"
        onPress={onAbout}
      />
    </Card>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
});