// src/features/profile/screens/ProfileScreen.tsx
import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  Text,
  Alert,
  StatusBar,
  Animated,
} from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import * as Haptics from 'expo-haptics';

// Components
import { ProfileHeader } from '../components/ProfileHeader';
import { SettingsRow } from '@/src/components/ui/SettingsRow';
import { Card } from '@/src/components/ui/Card';
import { Modal } from '@/src/components/ui/Modal';

// Modals
import { EditNameModal, EditPhoneModal, EditEmailModal, EditAddressModal } from '../components/EditModals';

import { tokens } from '@/src/core/theme/tokens';
import { useProfile } from '../hooks/useProfile';

export default function ProfileScreen() {
  const router = useRouter();
  const insets = useSafeAreaInsets();
  const { profile, update } = useProfile();
  const scrollY = useRef(new Animated.Value(0)).current;
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;
  
  // Modal states
  const [editNameVisible, setEditNameVisible] = useState(false);
  const [editPhoneVisible, setEditPhoneVisible] = useState(false);
  const [editEmailVisible, setEditEmailVisible] = useState(false);
  const [editAddressVisible, setEditAddressVisible] = useState(false);

  // Preferences
  const [notifications, setNotifications] = useState(true);
  const [smsReminders, setSmsReminders] = useState(true);
  const [emailUpdates, setEmailUpdates] = useState(false);
  const [autoSchedule, setAutoSchedule] = useState(true);

  useEffect(() => {
    // Entrance animation
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.spring(slideAnim, {
        toValue: 0,
        tension: 20,
        friction: 7,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const handleLogout = () => {
    Alert.alert(
      'התנתקות',
      'האם אתה בטוח שברצונך להתנתק?',
      [
        { text: 'ביטול', style: 'cancel' },
        {
          text: 'התנתק',
          style: 'destructive',
          onPress: () => router.replace('/(auth)/welcome'),
        },
      ]
    );
  };

  const handleDeleteAccount = () => {
    Alert.alert(
      'מחיקת חשבון',
      'פעולה זו תמחק את כל הנתונים שלך ולא ניתנת לביטול.',
      [
        { text: 'ביטול', style: 'cancel' },
        {
          text: 'מחק חשבון',
          style: 'destructive',
          onPress: () => console.log('Delete account'),
        },
      ]
    );
  };

  if (!profile) return null;

  const HEADER_HEIGHT = 280 + insets.top;

  return (
    <>
      <Stack.Screen options={{ headerShown: false }} />
      <StatusBar barStyle="dark-content" />
      
      <View style={styles.container}>
        <ProfileHeader
          insets={insets}
          scrollY={scrollY}
          user={profile}
          onBack={() => router.back()}
          onImageChange={(uri) => update({ profileImage: uri })}
        />
        
        <Animated.ScrollView
          style={styles.content}
          contentContainerStyle={[
            styles.scrollContent,
            { paddingTop: HEADER_HEIGHT },
          ]}
          showsVerticalScrollIndicator={false}
          onScroll={Animated.event(
            [{ nativeEvent: { contentOffset: { y: scrollY } } }],
            { useNativeDriver: false }
          )}
          scrollEventThrottle={16}
        >
          <Animated.View
            style={{
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }],
            }}
          >
            {/* Account Information */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>פרטי חשבון</Text>
              <Card variant="default" padding="none" style={styles.card}>
                <SettingsRow
                  icon="person-outline"
                  iconColor={tokens.colors.primary}
                  label="שם מלא"
                  value={profile.fullName}
                  onPress={() => setEditNameVisible(true)}
                />
                <SettingsRow
                  icon="call-outline"
                  iconColor={tokens.colors.success}
                  label="טלפון"
                  value={profile.phone}
                  description="מספר הטלפון הראשי שלך"
                  onPress={() => setEditPhoneVisible(true)}
                />
                <SettingsRow
                  icon="mail-outline"
                  iconColor={tokens.colors.info}
                  label="אימייל"
                  value={profile.email}
                  onPress={() => setEditEmailVisible(true)}
                />
                <SettingsRow
                  icon="location-outline"
                  iconColor={tokens.colors.warning}
                  label="כתובת"
                  value={`${profile.address.city}, ${profile.address.street}`}
                  description="כתובת ברירת מחדל לשיעורים"
                  onPress={() => setEditAddressVisible(true)}
                  isLast
                />
              </Card>
            </View>

            {/* Preferences with animations */}
            <Animated.View
              style={[
                styles.section,
                {
                  transform: [
                    {
                      scale: scrollY.interpolate({
                        inputRange: [-50, 0, 50],
                        outputRange: [1.05, 1, 1],
                        extrapolate: 'clamp',
                      }),
                    },
                  ],
                },
              ]}
            >
              <Text style={styles.sectionTitle}>העדפות</Text>
              <Card variant="default" padding="none" style={styles.card}>
                <SettingsRow
                  icon="notifications-outline"
                  iconColor={tokens.colors.primary}
                  label="התראות"
                  description="קבל עדכונים על שיעורים"
                  toggle
                  toggleValue={notifications}
                  onToggle={setNotifications}
                />
                <SettingsRow
                  icon="chatbubble-outline"
                  iconColor={tokens.colors.success}
                  label="תזכורות SMS"
                  toggle
                  toggleValue={smsReminders}
                  onToggle={setSmsReminders}
                />
                <SettingsRow
                  icon="mail-outline"
                  iconColor={tokens.colors.info}
                  label="עדכוני אימייל"
                  toggle
                  toggleValue={emailUpdates}
                  onToggle={setEmailUpdates}
                />
                <SettingsRow
                  icon="time-outline"
                  iconColor={tokens.colors.warning}
                  label="תזמון אוטומטי"
                  description="אפשר למערכת לתזמן שיעורים"
                  toggle
                  toggleValue={autoSchedule}
                  onToggle={setAutoSchedule}
                  isLast
                />
              </Card>
            </Animated.View>

            {/* Support */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>תמיכה</Text>
              <Card variant="default" padding="none" style={styles.card}>
                <SettingsRow
                  icon="help-circle-outline"
                  iconColor="#8B5CF6"
                  label="שאלות נפוצות"
                  onPress={() => router.push('/faq')}
                />
                <SettingsRow
                  icon="chatbubbles-outline"
                  iconColor="#06B6D4"
                  label="צור קשר"
                  description="נשמח לעזור!"
                  onPress={() => router.push('/contact')}
                />
                <SettingsRow
                  icon="star-outline"
                  iconColor="#F59E0B"
                  label="דרג את האפליקציה"
                  onPress={() => {}}
                  isLast
                />
              </Card>
            </View>

            {/* Legal */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>משפטי</Text>
              <Card variant="default" padding="none" style={styles.card}>
                <SettingsRow
                  icon="document-text-outline"
                  label="תנאי שימוש"
                  onPress={() => router.push('/terms')}
                />
                <SettingsRow
                  icon="shield-checkmark-outline"
                  label="מדיניות פרטיות"
                  onPress={() => router.push('/privacy')}
                />
                <SettingsRow
                  icon="information-circle-outline"
                  label="אודות"
                  onPress={() => router.push('/about')}
                  isLast
                />
              </Card>
            </View>

            {/* Account Actions with gradient background */}
            <View style={styles.section}>
              <Card variant="gradient" gradientColors={['#FEE2E2', '#FECACA']} padding="none" style={styles.card}>
                <SettingsRow
                  icon="log-out-outline"
                  iconColor={tokens.colors.error}
                  label="התנתק"
                  onPress={handleLogout}
                  showArrow={false}
                  destructive
                />
                <SettingsRow
                  icon="trash-outline"
                  iconColor={tokens.colors.error}
                  label="מחק חשבון"
                  description="פעולה זו אינה ניתנת לביטול"
                  onPress={handleDeleteAccount}
                  showArrow={false}
                  destructive
                  isLast
                />
              </Card>
            </View>

            <Text style={styles.version}>גרסה 1.0.0</Text>
          </Animated.View>
        </Animated.ScrollView>

        {/* Edit Modals using shared Modal component */}
        <EditNameModal
          visible={editNameVisible}
          currentName={profile.fullName}
          onClose={() => setEditNameVisible(false)}
          onSave={(name) => update({ fullName: name })}
        />
        <EditPhoneModal
          visible={editPhoneVisible}
          currentPhone={profile.phone}
          onClose={() => setEditPhoneVisible(false)}
          onSave={(phone) => update({ phone })}
        />
        <EditEmailModal
          visible={editEmailVisible}
          currentEmail={profile.email}
          onClose={() => setEditEmailVisible(false)}
          onSave={(email) => update({ email })}
        />
        <EditAddressModal
          visible={editAddressVisible}
          currentAddress={profile.address}
          onClose={() => setEditAddressVisible(false)}
          onSave={(address) => update({ address })}
        />
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F7FA',
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 40,
  },
  section: {
    marginTop: 24,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: '700',
    color: tokens.colors.textSecondary,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
    marginBottom: 10,
    marginHorizontal: 20,
  },
  card: {
    marginHorizontal: 16,
    borderRadius: 16,
    overflow: 'hidden',
  },
  version: {
    textAlign: 'center',
    fontSize: 12,
    color: tokens.colors.textTertiary,
    marginTop: 32,
    marginBottom: 20,
  },
});