// services/api/UserManager.ts
import { UserProfile } from '@/src/features/profile/types';

// Mock data - replace with real API
const mockProfile: UserProfile = {
  id: '1',
  fullName: 'ישראל ישראלי',
  email: '<EMAIL>',
  phone: '************',
  profileImage: null,
  memberSince: 'ינואר 2024',
  address: {
    city: 'תל אביב',
    street: 'רוטשילד',
    buildingNumber: '12',
    apartment: '5',
  },
  instructor: {
    id: '2',
    name: 'דוד כהן',
    phone: '************',
    email: '<EMAIL>',
    rating: 4.8,
    connected: true,
  },
  preferences: {
    notifications: true,
    smsReminders: true,
    emailUpdates: false,
    autoSchedule: true,
    language: 'he',
    theme: 'light',
  },
  allowList: [
    { id: '3', name: 'אמ<PERSON>', phone: '************', relationship: 'parent' },
    { id: '4', name: 'אב<PERSON>', phone: '************', relationship: 'parent' },
  ],
  stats: {
    lessonsCompleted: 24,
    hoursCompleted: 36,
    upcomingLessons: 3,
    averageRating: 4.8,
    totalDistance: 450,
    successRate: 85,
  },
};

export const UserManager = {
  getUser: async (): Promise<UserProfile> => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    return mockProfile;
  },
  
  updateUser: async (updates: Partial<UserProfile>): Promise<UserProfile> => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300));
    Object.assign(mockProfile, updates);
    return mockProfile;
  },
  
  clearUser: async (): Promise<void> => {
    // Clear local storage
    await new Promise(resolve => setTimeout(resolve, 100));
  },
};