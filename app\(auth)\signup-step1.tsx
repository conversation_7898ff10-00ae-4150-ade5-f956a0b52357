import { Stack, useRouter } from "expo-router";
import React, { useEffect, useRef, useState } from "react";
import {
  Animated,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
  Keyboard,
  StatusBar,
  Dimensions,
  Pressable,
  ActivityIndicator,
} from "react-native";
import { BlurView } from "expo-blur";
import { LinearGradient } from "expo-linear-gradient";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { Ionicons } from "@expo/vector-icons";
import * as Haptics from "expo-haptics";
import { RegistrationAPI, RegistrationManager, APIError, APIUtils } from "../../services/api";

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get("window");

interface FormData {
  fullName: string;
  phone: string;
  email: string;
}

export default function SignupStep1() {
  const router = useRouter();
  const insets = useSafeAreaInsets();
  const scrollViewRef = useRef<ScrollView>(null);
  
  const [formData, setFormData] = useState<FormData>({
    fullName: "",
    phone: "",
    email: "",
  });
  
  const [focusedField, setFocusedField] = useState<keyof FormData | null>(null);
  const [errors, setErrors] = useState<Partial<FormData>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [apiError, setApiError] = useState<string | null>(null);
  
  // Animation values
  const titleOpacity = useRef(new Animated.Value(0)).current;
  const titleTranslateY = useRef(new Animated.Value(20)).current;
  const progressScale = useRef(new Animated.Value(0)).current;
  const field1Opacity = useRef(new Animated.Value(0)).current;
  const field1TranslateX = useRef(new Animated.Value(-30)).current;
  const field2Opacity = useRef(new Animated.Value(0)).current;
  const field2TranslateX = useRef(new Animated.Value(-30)).current;
  const field3Opacity = useRef(new Animated.Value(0)).current;
  const field3TranslateX = useRef(new Animated.Value(-30)).current;
  const buttonTranslateY = useRef(new Animated.Value(100)).current;
  const buttonOpacity = useRef(new Animated.Value(0)).current;
  const blob1Translate = useRef(new Animated.Value(0)).current;
  const blob2Translate = useRef(new Animated.Value(0)).current;
  const backButtonScale = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Entrance animations with proper cascade sequence (faster than original, but maintaining order)
    Animated.sequence([
      // Back button entrance
      Animated.spring(backButtonScale, {
        toValue: 1,
        tension: 30,
        friction: 8,
        useNativeDriver: true,
      }),
      // Title and progress
      Animated.parallel([
        Animated.timing(titleOpacity, {
          toValue: 1,
          duration: 500,
          useNativeDriver: true,
        }),
        Animated.spring(titleTranslateY, {
          toValue: 0,
          tension: 30,
          friction: 8,
          useNativeDriver: true,
        }),
        Animated.spring(progressScale, {
          toValue: 1,
          tension: 20,
          friction: 7,
          delay: 100,
          useNativeDriver: true,
        }),
      ]),
      // Fields cascade with proper sequential timing
      Animated.stagger(100, [
        Animated.parallel([
          Animated.timing(field1Opacity, {
            toValue: 1,
            duration: 450,
            useNativeDriver: true,
          }),
          Animated.spring(field1TranslateX, {
            toValue: 0,
            tension: 30,
            friction: 8,
            useNativeDriver: true,
          }),
        ]),
        Animated.parallel([
          Animated.timing(field2Opacity, {
            toValue: 1,
            duration: 450,
            useNativeDriver: true,
          }),
          Animated.spring(field2TranslateX, {
            toValue: 0,
            tension: 30,
            friction: 8,
            useNativeDriver: true,
          }),
        ]),
        Animated.parallel([
          Animated.timing(field3Opacity, {
            toValue: 1,
            duration: 450,
            useNativeDriver: true,
          }),
          Animated.spring(field3TranslateX, {
            toValue: 0,
            tension: 30,
            friction: 8,
            useNativeDriver: true,
          }),
        ]),
      ]),
      // Button entrance
      Animated.parallel([
        Animated.spring(buttonTranslateY, {
          toValue: 0,
          tension: 30,
          friction: 8,
          useNativeDriver: true,
        }),
        Animated.timing(buttonOpacity, {
          toValue: 1,
          duration: 500,
          useNativeDriver: true,
        }),
      ]),
    ]).start();

    // Floating blob animations
    const blob1Animation = Animated.loop(
      Animated.sequence([
        Animated.timing(blob1Translate, {
          toValue: 25,
          duration: 3500,
          useNativeDriver: true,
        }),
        Animated.timing(blob1Translate, {
          toValue: -25,
          duration: 3500,
          useNativeDriver: true,
        }),
      ])
    );

    const blob2Animation = Animated.loop(
      Animated.sequence([
        Animated.timing(blob2Translate, {
          toValue: -20,
          duration: 4000,
          useNativeDriver: true,
        }),
        Animated.timing(blob2Translate, {
          toValue: 20,
          duration: 4000,
          useNativeDriver: true,
        }),
      ])
    );

    blob1Animation.start();
    blob2Animation.start();

    return () => {
      blob1Animation.stop();
      blob2Animation.stop();
    };
  }, []);

  const handleBack = async () => {
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    router.back();
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<FormData> = {};
    
    if (!formData.fullName.trim()) {
      newErrors.fullName = "שם מלא נדרש";
    }
    
    if (!formData.phone.trim()) {
      newErrors.phone = "מספר טלפון נדרש";
    } else if (!/^[0-9]{10}$/.test(formData.phone.replace(/[^0-9]/g, ""))) {
      newErrors.phone = "מספר טלפון לא תקין";
    }
    
    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = "כתובת אימייל לא תקינה";
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleContinue = async () => {
    if (!validateForm()) {
      await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      return;
    }
    
    setIsLoading(true);
    setApiError(null);
    
    try {
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      Keyboard.dismiss();
      
      // Call the registration API
      const response = await RegistrationAPI.startRegistration({
        fullName: formData.fullName,
        phone: formData.phone,
        email: formData.email || undefined,
      });
      
      // Save regId securely
      await RegistrationManager.saveRegId(response.regId);
      
      // Navigate to step 2
      router.push("/(auth)/signup-step2");
      
    } catch (error) {
      await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      
      let errorMessage = 'שגיאה בהרשמה. נסה שוב.';
      
      if (error instanceof APIError) {
        if (APIUtils.isDuplicatePhoneError(error)) {
          errorMessage = 'מספר הטלפון כבר רשום במערכת';
          setErrors({ ...errors, phone: errorMessage });
        } else if (APIUtils.isNetworkError(error)) {
          errorMessage = 'בעיית רשת. בדוק חיבור אינטרנט ונסה שוב.';
        } else {
          errorMessage = error.message;
        }
      }
      
      setApiError(errorMessage);
      console.error('Registration error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const formatPhoneNumber = (text: string) => {
    const cleaned = text.replace(/[^0-9]/g, "");
    let formatted = cleaned;
    
    if (cleaned.length > 0) {
      if (cleaned.length <= 3) {
        formatted = cleaned;
      } else if (cleaned.length <= 6) {
        formatted = `${cleaned.slice(0, 3)}-${cleaned.slice(3)}`;
      } else {
        formatted = `${cleaned.slice(0, 3)}-${cleaned.slice(3, 6)}-${cleaned.slice(6, 10)}`;
      }
    }
    
    return formatted;
  };

  const isFormValid = formData.fullName && formData.phone && !isLoading;

  return (
    <>
      <Stack.Screen options={{ headerShown: false }} />
      <StatusBar barStyle="dark-content" />
      
      <LinearGradient
        colors={["#EDF3FF", "#EAF1FF", "#E7EEFF"]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.container}
      >
        {/* Floating blobs */}
        <View style={styles.blobs} pointerEvents="none">
          <Animated.View 
            style={[
              styles.blob, 
              styles.blobA,
              {
                transform: [
                  { translateX: blob1Translate },
                  { translateY: Animated.multiply(blob1Translate, 0.3) }
                ]
              }
            ]} 
          />
          <Animated.View 
            style={[
              styles.blob, 
              styles.blobB,
              {
                transform: [
                  { translateX: blob2Translate },
                  { translateY: Animated.multiply(blob2Translate, -0.5) }
                ]
              }
            ]} 
          />
        </View>

        {/* Header */}
        <View style={[styles.header, { paddingTop: insets.top + 10 }]}>
          {/* Back button */}
          <Animated.View style={{ transform: [{ scale: backButtonScale }] }}>
            <Pressable
              onPress={handleBack}
              style={({ pressed }) => [
                styles.backButton,
                { transform: [{ scale: pressed ? 0.9 : 1 }] }
              ]}
            >
              <BlurView intensity={30} tint="light" style={styles.backButtonBlur}>
                <Ionicons name="chevron-back" size={24} color="#445EFB" />
              </BlurView>
            </Pressable>
          </Animated.View>

          {/* Progress dots */}
          <Animated.View 
            style={[
              styles.progressDots,
              { transform: [{ scale: progressScale }] }
            ]}
          >
            <View style={[styles.dot, styles.dotActive]} />
            <View style={styles.dot} />
          </Animated.View>

          {/* Step indicator */}
          <View style={styles.stepBadge}>
            <Text style={styles.stepText}>1/2</Text>
          </View>
        </View>

        {/* Title section */}
        <Animated.View 
          style={[
            styles.titleSection,
            {
              opacity: titleOpacity,
              transform: [{ translateY: titleTranslateY }]
            }
          ]}
        >
          <Text style={styles.titleKicker}>שלב ראשון</Text>
          <Text style={styles.title}>פרטים אישיים</Text>
          <Text style={styles.subtitle}>נתחיל עם המידע הבסיסי שלך</Text>
        </Animated.View>

        {/* Form */}
        <KeyboardAvoidingView
          behavior={Platform.OS === "ios" ? "padding" : "height"}
          style={styles.formContainer}
        >
          <ScrollView
            ref={scrollViewRef}
            contentContainerStyle={styles.scrollContent}
            showsVerticalScrollIndicator={false}
            keyboardShouldPersistTaps="handled"
          >
            <View style={styles.form}>
              {/* Full Name Field */}
              <Animated.View 
                style={[
                  styles.inputGroup,
                  {
                    opacity: field1Opacity,
                    transform: [{ translateX: field1TranslateX }]
                  }
                ]}
              >
                <Text style={styles.label}>שם מלא</Text>
                <Pressable 
                  onPress={() => {}} 
                  style={[
                    styles.inputContainer,
                    focusedField === "fullName" && styles.inputContainerFocused,
                    errors.fullName && styles.inputContainerError,
                  ]}
                >
                  <BlurView 
                    intensity={focusedField === "fullName" ? 0 : 15} 
                    tint="light" 
                    style={styles.inputBlur}
                  >
                    <View style={styles.inputContent}>
                      <View style={[
                        styles.inputIconWrapper,
                        focusedField === "fullName" && styles.inputIconWrapperActive
                      ]}>
                        <Ionicons 
                          name="person" 
                          size={18} 
                          color={focusedField === "fullName" ? "#445EFB" : "#9CA3AF"} 
                        />
                      </View>
                      <TextInput
                        value={formData.fullName}
                        onChangeText={(text) => {
                          setFormData({ ...formData, fullName: text });
                          if (errors.fullName) setErrors({ ...errors, fullName: undefined });
                        }}
                        onFocus={() => setFocusedField("fullName")}
                        onBlur={() => setFocusedField(null)}
                        placeholder="ישראל ישראלי"
                        placeholderTextColor="#9CA3AF"
                        style={styles.input}
                        autoCapitalize="words"
                        returnKeyType="next"
                      />
                    </View>
                  </BlurView>
                </Pressable>
                {errors.fullName && (
                  <Animated.Text style={styles.errorText}>
                    {errors.fullName}
                  </Animated.Text>
                )}
              </Animated.View>

              {/* Phone Field */}
              <Animated.View 
                style={[
                  styles.inputGroup,
                  {
                    opacity: field2Opacity,
                    transform: [{ translateX: field2TranslateX }]
                  }
                ]}
              >
                <Text style={styles.label}>מספר טלפון</Text>
                <Pressable 
                  onPress={() => {}} 
                  style={[
                    styles.inputContainer,
                    focusedField === "phone" && styles.inputContainerFocused,
                    errors.phone && styles.inputContainerError,
                  ]}
                >
                  <BlurView 
                    intensity={focusedField === "phone" ? 0 : 15} 
                    tint="light" 
                    style={styles.inputBlur}
                  >
                    <View style={styles.inputContent}>
                      <View style={[
                        styles.inputIconWrapper,
                        focusedField === "phone" && styles.inputIconWrapperActive
                      ]}>
                        <Ionicons 
                          name="call" 
                          size={18} 
                          color={focusedField === "phone" ? "#445EFB" : "#9CA3AF"} 
                        />
                      </View>
                      <View style={styles.phonePrefixWrapper}>
                        <Text style={styles.phonePrefix}>+972</Text>
                      </View>
                      <TextInput
                        value={formData.phone}
                        onChangeText={(text) => {
                          const formatted = formatPhoneNumber(text);
                          setFormData({ ...formData, phone: formatted });
                          if (errors.phone) setErrors({ ...errors, phone: undefined });
                        }}
                        onFocus={() => setFocusedField("phone")}
                        onBlur={() => setFocusedField(null)}
                        placeholder="50-123-4567"
                        placeholderTextColor="#9CA3AF"
                        style={styles.input}
                        keyboardType="phone-pad"
                        maxLength={12}
                        returnKeyType="next"
                      />
                    </View>
                  </BlurView>
                </Pressable>
                {errors.phone && (
                  <Animated.Text style={styles.errorText}>
                    {errors.phone}
                  </Animated.Text>
                )}
              </Animated.View>

              {/* Email Field */}
              <Animated.View 
                style={[
                  styles.inputGroup,
                  {
                    opacity: field3Opacity,
                    transform: [{ translateX: field3TranslateX }]
                  }
                ]}
              >
                <View style={styles.labelRow}>
                  <Text style={styles.label}>אימייל</Text>
                  <Text style={styles.labelOptional}>אופציונלי</Text>
                </View>
                <Pressable 
                  onPress={() => {}} 
                  style={[
                    styles.inputContainer,
                    focusedField === "email" && styles.inputContainerFocused,
                    errors.email && styles.inputContainerError,
                  ]}
                >
                  <BlurView 
                    intensity={focusedField === "email" ? 0 : 15} 
                    tint="light" 
                    style={styles.inputBlur}
                  >
                    <View style={styles.inputContent}>
                      <View style={[
                        styles.inputIconWrapper,
                        focusedField === "email" && styles.inputIconWrapperActive
                      ]}>
                        <Ionicons 
                          name="mail" 
                          size={18} 
                          color={focusedField === "email" ? "#445EFB" : "#9CA3AF"} 
                        />
                      </View>
                      <TextInput
                        value={formData.email}
                        onChangeText={(text) => {
                          setFormData({ ...formData, email: text });
                          if (errors.email) setErrors({ ...errors, email: undefined });
                        }}
                        onFocus={() => setFocusedField("email")}
                        onBlur={() => setFocusedField(null)}
                        placeholder="<EMAIL>"
                        placeholderTextColor="#9CA3AF"
                        style={styles.input}
                        keyboardType="email-address"
                        autoCapitalize="none"
                        returnKeyType="done"
                      />
                    </View>
                  </BlurView>
                </Pressable>
                {errors.email && (
                  <Animated.Text style={styles.errorText}>
                    {errors.email}
                  </Animated.Text>
                )}
              </Animated.View>
            </View>
          </ScrollView>
        </KeyboardAvoidingView>

        {/* Bottom section */}
        <Animated.View 
          style={[
            styles.bottomSection,
            { 
              paddingBottom: insets.bottom + 24,
              opacity: buttonOpacity,
              transform: [{ translateY: buttonTranslateY }]
            }
          ]}
        >
          {/* API Error Display */}
          {apiError && (
            <View style={styles.errorContainer}>
              <Ionicons name="alert-circle" size={18} color="#EF4444" />
              <Text style={styles.errorMessage}>{apiError}</Text>
            </View>
          )}

          {/* Continue Button */}
          <Pressable
            onPress={handleContinue}
            disabled={!isFormValid}
            style={({ pressed }) => [
              styles.continueButton,
              !isFormValid && styles.continueButtonDisabled,
              { transform: [{ scale: pressed && isFormValid ? 0.97 : 1 }] }
            ]}
          >
            {({ pressed }) => (
              <LinearGradient
                colors={
                  isFormValid 
                    ? (pressed ? ["#3A52E3", "#5E98F3"] : ["#445EFB", "#6EA8FF"])
                    : ["#E5E7EB", "#E5E7EB"]
                }
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
                style={styles.continueButtonGradient}
              >
                {isLoading ? (
                  <ActivityIndicator color="#fff" size="small" />
                ) : (
                  <>
                    <Text style={[
                      styles.continueButtonText,
                      !isFormValid && styles.continueButtonTextDisabled
                    ]}>
                      המשך
                    </Text>
                    <View style={[
                      styles.continueButtonArrow,
                      !isFormValid && styles.continueButtonArrowDisabled
                    ]}>
                      <Ionicons 
                        name="arrow-forward" 
                        size={20} 
                        color={isFormValid ? "#fff" : "#9CA3AF"} 
                      />
                    </View>
                  </>
                )}
              </LinearGradient>
            )}
          </Pressable>

          {/* Privacy note */}
          <Text style={styles.privacyNote}>
            המידע שלך מאובטח ולא ישותף עם צד שלישי
          </Text>
        </Animated.View>
      </LinearGradient>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  
  // Blobs
  blobs: {
    position: "absolute",
    width: "100%",
    height: "100%",
  },
  blob: {
    position: "absolute",
    borderRadius: 999,
    backgroundColor: "#ffffff",
    ...Platform.select({
      ios: {
        shadowColor: "#4C6BFF",
        shadowOpacity: 0.1,
        shadowRadius: 40,
        shadowOffset: { width: 0, height: 10 }
      },
      android: { elevation: 0 },
    }),
  },
  blobA: {
    top: 60,
    right: -80,
    width: 200,
    height: 200,
    opacity: 0.35,
  },
  blobB: {
    bottom: 150,
    left: -100,
    width: 250,
    height: 250,
    opacity: 0.3,
  },

  // Header
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 22,
    paddingBottom: 20,
  },
  backButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    overflow: "hidden",
  },
  backButtonBlur: {
    width: "100%",
    height: "100%",
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(255, 255, 255, 0.75)",
    borderWidth: StyleSheet.hairlineWidth,
    borderColor: "rgba(0, 0, 0, 0.08)",
  },
  progressDots: {
    flexDirection: "row",
    gap: 8,
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: "rgba(68, 94, 251, 0.2)",
  },
  dotActive: {
    width: 24,
    backgroundColor: "#445EFB",
  },
  stepBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 14,
    backgroundColor: "rgba(68, 94, 251, 0.1)",
  },
  stepText: {
    fontSize: 12,
    fontWeight: "700",
    color: "#445EFB",
  },

  // Title section
  titleSection: {
    alignItems: "center",
    paddingHorizontal: 28,
    marginBottom: 32,
  },
  titleKicker: {
    fontSize: 13,
    fontWeight: "700",
    color: "#5873FF",
    letterSpacing: 1,
    marginBottom: 8,
  },
  title: {
    fontSize: 32,
    fontWeight: "800",
    color: "#0B0B0B",
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 15,
    color: "rgba(0, 0, 0, 0.6)",
  },

  // Form
  formContainer: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: 22,
    paddingBottom: 150,
  },
  form: {
    gap: 20,
  },
  inputGroup: {
    gap: 10,
  },
  labelRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  label: {
    fontSize: 14,
    fontWeight: "700",
    color: "#0B0B0B",
    marginLeft: 4,
  },
  labelOptional: {
    fontSize: 12,
    fontWeight: "500",
    color: "rgba(0, 0, 0, 0.4)",
    marginRight: 4,
  },
  inputContainer: {
    height: 60,
    borderRadius: 16,
    overflow: "hidden",
    borderWidth: 1.5,
    borderColor: "transparent",
    backgroundColor: "rgba(255, 255, 255, 0.5)",
    ...Platform.select({
      ios: {
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.05,
        shadowRadius: 8,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  inputContainerFocused: {
    borderColor: "#445EFB",
    backgroundColor: "#fff",
    ...Platform.select({
      ios: {
        shadowColor: "#445EFB",
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.15,
        shadowRadius: 12,
      },
      android: {
        elevation: 4,
      },
    }),
  },
  inputContainerError: {
    borderColor: "#EF4444",
  },
  inputBlur: {
    flex: 1,
  },
  inputContent: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
  },
  inputIconWrapper: {
    width: 32,
    height: 32,
    borderRadius: 10,
    backgroundColor: "rgba(68, 94, 251, 0.08)",
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  inputIconWrapperActive: {
    backgroundColor: "rgba(68, 94, 251, 0.15)",
  },
  phonePrefixWrapper: {
    paddingRight: 12,
    marginRight: 12,
    borderRightWidth: 1,
    borderRightColor: "rgba(0, 0, 0, 0.1)",
  },
  phonePrefix: {
    fontSize: 16,
    fontWeight: "600",
    color: "#6B7280",
  },
  input: {
    flex: 1,
    fontSize: 16,
    fontWeight: "500",
    color: "#0B0B0B",
  },
  errorText: {
    fontSize: 12,
    fontWeight: "600",
    color: "#EF4444",
    marginLeft: 4,
  },

  // Bottom section
  bottomSection: {
    paddingHorizontal: 22,
  },
  errorContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
    backgroundColor: "rgba(239, 68, 68, 0.1)",
    borderWidth: 1,
    borderColor: "rgba(239, 68, 68, 0.2)",
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginBottom: 16,
  },
  errorMessage: {
    flex: 1,
    fontSize: 14,
    fontWeight: "600",
    color: "#EF4444",
    textAlign: "right",
  },
  continueButton: {
    borderRadius: 16,
    overflow: "hidden",
    marginBottom: 16,
    ...Platform.select({
      ios: {
        shadowColor: "#445EFB",
        shadowOpacity: 0.3,
        shadowRadius: 20,
        shadowOffset: { width: 0, height: 10 },
      },
      android: {
        elevation: 8,
      },
    }),
  },
  continueButtonDisabled: {
    ...Platform.select({
      ios: {
        shadowOpacity: 0.08,
        shadowColor: "#000",
      },
      android: {
        elevation: 2,
      },
    }),
  },
  continueButtonGradient: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 18,
    gap: 8,
  },
  continueButtonText: {
    fontSize: 17,
    fontWeight: "800",
    color: "#fff",
    letterSpacing: 0.3,
  },
  continueButtonTextDisabled: {
    color: "#9CA3AF",
  },
  continueButtonArrow: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: "rgba(255, 255, 255, 0.25)",
    alignItems: "center",
    justifyContent: "center",
  },
  continueButtonArrowDisabled: {
    backgroundColor: "rgba(0, 0, 0, 0.05)",
  },
  privacyNote: {
    textAlign: "center",
    fontSize: 12,
    fontWeight: "600",
    color: "rgba(0, 0, 0, 0.4)",
    letterSpacing: 0.3,
  },
});