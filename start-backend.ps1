# start-backend.ps1
Write-Host "Starting DLSA Backend..." -ForegroundColor Green

# Start MongoDB in background
Start-Process -FilePath "C:\Program Files\MongoDB\Server\8.0\bin\mongod.exe" `
    -ArgumentList "--port", "27018", "--dbpath", "C:\mongo\dlsa_app\data", "--bind_ip", "127.0.0.1" `
    -WindowStyle Minimized

Write-Host "MongoDB started on port 27018" -ForegroundColor Yellow

# Wait a bit for MongoDB to start
Start-Sleep -Seconds 3

# Navigate to backend directory
Set-Location -Path ".\backend"

# Install dependencies if needed
if (-not (Test-Path ".\node_modules")) {
    Write-Host "Installing dependencies..." -ForegroundColor Yellow
    npm install
}

# Build TypeScript
Write-Host "Building TypeScript..." -ForegroundColor Yellow
npm run build

# Start the server
Write-Host "Starting server on port 3000..." -ForegroundColor Green
npm start