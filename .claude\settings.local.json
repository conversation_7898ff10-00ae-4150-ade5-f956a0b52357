{"permissions": {"allow": ["Bash(Get-NetTCPConnection -LocalPort 3000 -ErrorAction SilentlyContinue)", "<PERSON><PERSON>(powershell:*)", "Bash(set PORT=3000)", "Bash(set MONGODB_URI=mongodb://127.0.0.1:27018/DLSA_app)", "Bash(set DEV_ADMIN_TOKEN=dev-only-secret)", "Bash(set CORS_ORIGIN=*)", "Bash(set NODE_ENV=development)", "Bash(npm run dev:*)", "<PERSON><PERSON>(cat:*)", "Bash(PORT=3000 MONGODB_URI=mongodb://127.0.0.1:27018/DLSA_app DEV_ADMIN_TOKEN=dev-only-secret NODE_ENV=development npm run dev)", "<PERSON><PERSON>(curl:*)", "Bash(grep:*)", "Bash(npm install:*)", "Bash(npx tsx:*)", "Bash(timeout 10s npx tsx src/server.ts)", "Bash(node:*)", "Bash(npm run build:*)", "Bash(npm start)", "Bash(REG_ID=\"************************************\")"], "deny": [], "ask": []}}