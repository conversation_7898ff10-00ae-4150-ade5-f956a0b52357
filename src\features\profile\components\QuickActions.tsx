// src/features/profile/components/QuickActions.tsx
import React from 'react';
import { View, Text, Pressable, StyleSheet } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import { Card } from '@/src/components';
import { tokens } from '@/src/core/theme/tokens';

interface Props {
  onEditProfile: () => void;
  onHistory: () => void;
  onDocuments: () => void;
  onAchievements: () => void;
}

export function QuickActions({ onEditProfile, onHistory, onDocuments, onAchievements }: Props) {
  const actions = [
    {
      icon: 'create-outline' as const,
      label: 'ערוך פרופיל',
      colors: ['#EFF6FF', '#DBEAFE'],
      iconColor: tokens.colors.primary,
      onPress: onEditProfile,
    },
    {
      icon: 'time-outline' as const,
      label: 'היסטוריה',
      colors: ['#F0FDF4', '#BBF7D0'],
      iconColor: tokens.colors.success,
      onPress: onHistory,
    },
    {
      icon: 'document-text-outline' as const,
      label: 'מסמכים',
      colors: ['#FFFBEB', '#FED7AA'],
      iconColor: tokens.colors.warning,
      onPress: onDocuments,
    },
    {
      icon: 'trophy-outline' as const,
      label: 'הישגים',
      colors: ['#FEF3C7', '#FDE047'],
      iconColor: '#D97706',
      onPress: onAchievements,
    },
  ];

  return (
    <Card variant="elevated" style={styles.container}>
      <Text style={styles.title}>פעולות מהירות</Text>
      <View style={styles.grid}>
        {actions.map((action, index) => (
          <Pressable
            key={index}
            onPress={action.onPress}
            style={({ pressed }) => [
              styles.action,
              pressed && styles.pressed,
            ]}
          >
            <View style={styles.iconWrapper}>
              <LinearGradient
                colors={action.colors as any}
                style={styles.iconGradient}
              >
                <Ionicons
                  name={action.icon}
                  size={24}
                  color={action.iconColor}
                />
              </LinearGradient>
            </View>
            <Text style={styles.label}>{action.label}</Text>
          </Pressable>
        ))}
      </View>
    </Card>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: '700',
    color: tokens.colors.textPrimary,
    marginBottom: 16,
  },
  grid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  action: {
    alignItems: 'center',
    flex: 1,
  },
  pressed: {
    opacity: 0.7,
    transform: [{ scale: 0.95 }],
  },
  iconWrapper: {
    width: 56,
    height: 56,
    borderRadius: 16,
    overflow: 'hidden',
    marginBottom: 8,
  },
  iconGradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  label: {
    fontSize: 13,
    fontWeight: '600',
    color: tokens.colors.textPrimary,
    textAlign: 'center',
  },
});