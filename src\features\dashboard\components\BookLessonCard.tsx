// src/features/dashboard/components/BookLessonCard.tsx
import React, { useState } from "react";
import {
  View,
  Text,
  Pressable,
  StyleSheet,
  Platform,
  ScrollView,
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { BlurView } from "expo-blur";
import { Ionicons, MaterialCommunityIcons } from "@expo/vector-icons";
import * as Haptics from "expo-haptics";

interface Props {
  instructor: {
    name: string;
    nextAvailable: string;
  };
  onBookLesson: (slot: any) => void;
  suggestedSlots?: Array<{
    id: string;
    date: string;
    time: string;
    available: boolean;
  }>;
}

export function BookLessonCard({ instructor, onBookLesson, suggestedSlots }: Props) {
  const [selectedSlot, setSelectedSlot] = useState<string | null>(null);

  const defaultSlots = suggestedSlots || [
    { id: "1", date: "היום", time: "14:00", available: true },
    { id: "2", date: "היום", time: "16:00", available: true },
    { id: "3", date: "מחר", time: "10:00", available: true },
    { id: "4", date: "מחר", time: "12:00", available: false },
    { id: "5", date: "ראשון", time: "09:00", available: true },
  ];

  const handleSlotPress = async (slotId: string) => {
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setSelectedSlot(slotId);
  };

  const handleBookPress = async () => {
    if (!selectedSlot) return;
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    const slot = defaultSlots.find(s => s.id === selectedSlot);
    if (slot) onBookLesson(slot);
  };

  return (
    <View style={styles.card}>
      {/* Animated gradient background */}
      <LinearGradient
        colors={["#F0F9FF", "#E0F2FE", "#BAE6FD"]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.gradientBackground}
      >
        {/* Floating decorative elements */}
        <View style={styles.floatingCircle1} />
        <View style={styles.floatingCircle2} />
        
        {/* Main Content */}
        <View style={styles.content}>
          {/* Header with icon */}
          <View style={styles.header}>
            <View style={styles.iconContainer}>
              <LinearGradient
                colors={["#3B82F6", "#2563EB"]}
                style={styles.iconGradient}
              >
                <MaterialCommunityIcons name="calendar-clock" size={28} color="#fff" />
              </LinearGradient>
            </View>
            
            <View style={styles.headerText}>
              <Text style={styles.title}>אין לך שיעור מתוכנן</Text>
              <Text style={styles.subtitle}>בוא נקבע את השיעור הבא שלך!</Text>
            </View>

            {/* Pulse animation dot */}
            <View style={styles.pulseContainer}>
              <View style={styles.pulseDot} />
              <View style={styles.pulseRing} />
            </View>
          </View>

          {/* Instructor info strip */}
          <View style={styles.instructorStrip}>
            <View style={styles.instructorLeft}>
              <View style={styles.miniAvatar}>
                <Text style={styles.miniInitial}>{instructor.name.charAt(0)}</Text>
              </View>
              <View>
                <Text style={styles.instructorName}>{instructor.name}</Text>
                <Text style={styles.availabilityText}>
                  זמין החל מ{instructor.nextAvailable}
                </Text>
              </View>
            </View>
            <View style={styles.statusBadge}>
              <View style={styles.statusDot} />
              <Text style={styles.statusText}>זמין</Text>
            </View>
          </View>

          {/* Quick slots selection */}
          <View style={styles.slotsSection}>
            <Text style={styles.slotsTitle}>זמנים מומלצים</Text>
            <ScrollView 
              horizontal 
              showsHorizontalScrollIndicator={false}
              style={styles.slotsScroll}
            >
              {defaultSlots.map((slot) => (
                <Pressable
                  key={slot.id}
                  onPress={() => slot.available && handleSlotPress(slot.id)}
                  disabled={!slot.available}
                  style={({ pressed }) => [
                    styles.slotCard,
                    selectedSlot === slot.id && styles.slotCardSelected,
                    !slot.available && styles.slotCardDisabled,
                    pressed && slot.available && styles.slotCardPressed,
                  ]}
                >
                  {selectedSlot === slot.id && (
                    <View style={styles.selectedCheck}>
                      <Ionicons name="checkmark-circle" size={20} color="#3B82F6" />
                    </View>
                  )}
                  <Text style={[
                    styles.slotDate,
                    selectedSlot === slot.id && styles.slotDateSelected,
                    !slot.available && styles.slotTextDisabled,
                  ]}>
                    {slot.date}
                  </Text>
                  <Text style={[
                    styles.slotTime,
                    selectedSlot === slot.id && styles.slotTimeSelected,
                    !slot.available && styles.slotTextDisabled,
                  ]}>
                    {slot.time}
                  </Text>
                  {!slot.available && (
                    <View style={styles.unavailableOverlay}>
                      <Text style={styles.unavailableText}>תפוס</Text>
                    </View>
                  )}
                </Pressable>
              ))}
            </ScrollView>
          </View>

          {/* Action buttons */}
          <View style={styles.actions}>
            <Pressable
              style={({ pressed }) => [
                styles.secondaryBtn,
                { transform: [{ scale: pressed ? 0.95 : 1 }] },
              ]}
              onPress={() => {}}
            >
              <Ionicons name="calendar-outline" size={20} color="#6B7280" />
              <Text style={styles.secondaryBtnText}>לוח שנה מלא</Text>
            </Pressable>

            <Pressable
              style={({ pressed }) => [
                styles.primaryBtn,
                !selectedSlot && styles.primaryBtnDisabled,
                { transform: [{ scale: pressed && selectedSlot ? 0.95 : 1 }] },
              ]}
              onPress={handleBookPress}
              disabled={!selectedSlot}
            >
              <LinearGradient
                colors={selectedSlot ? ["#3B82F6", "#2563EB"] : ["#CBD5E1", "#9CA3AF"]}
                style={styles.primaryBtnGradient}
              >
                <MaterialCommunityIcons 
                  name="check-bold" 
                  size={20} 
                  color="#fff" 
                />
                <Text style={styles.primaryBtnText}>קבע שיעור</Text>
              </LinearGradient>
            </Pressable>
          </View>
        </View>
      </LinearGradient>
    </View>
  );
}

const styles = StyleSheet.create({
  card: {
    borderRadius: 24,
    marginBottom: 16,
    overflow: "hidden",
    ...Platform.select({
      ios: {
        shadowColor: "#3B82F6",
        shadowOffset: { width: 0, height: 8 },
        shadowOpacity: 0.15,
        shadowRadius: 16,
      },
      android: {
        elevation: 8,
      },
    }),
  },
  gradientBackground: {
    position: "relative",
  },
  floatingCircle1: {
    position: "absolute",
    top: -40,
    right: -40,
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: "rgba(59, 130, 246, 0.1)",
  },
  floatingCircle2: {
    position: "absolute",
    bottom: -30,
    left: -30,
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: "rgba(59, 130, 246, 0.08)",
  },
  content: {
    padding: 20,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 20,
  },
  iconContainer: {
    width: 56,
    height: 56,
    borderRadius: 16,
    overflow: "hidden",
    marginRight: 12,
    ...Platform.select({
      ios: {
        shadowColor: "#3B82F6",
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.2,
        shadowRadius: 8,
      },
      android: {
        elevation: 4,
      },
    }),
  },
  iconGradient: {
    width: "100%",
    height: "100%",
    justifyContent: "center",
    alignItems: "center",
  },
  headerText: {
    flex: 1,
  },
  title: {
    fontSize: 20,
    fontWeight: "800",
    color: "#0F172A",
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 15,
    fontWeight: "500",
    color: "#64748B",
  },
  pulseContainer: {
    position: "relative",
    width: 24,
    height: 24,
    alignItems: "center",
    justifyContent: "center",
  },
  pulseDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: "#3B82F6",
  },
  pulseRing: {
    position: "absolute",
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: "#3B82F6",
    opacity: 0.3,
  },
  instructorStrip: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    backgroundColor: "rgba(255, 255, 255, 0.8)",
    borderRadius: 14,
    padding: 12,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: "rgba(59, 130, 246, 0.1)",
  },
  instructorLeft: {
    flexDirection: "row",
    alignItems: "center",
    gap: 10,
  },
  miniAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: "#E0E7FF",
    justifyContent: "center",
    alignItems: "center",
  },
  miniInitial: {
    fontSize: 14,
    fontWeight: "700",
    color: "#4338CA",
  },
  instructorName: {
    fontSize: 14,
    fontWeight: "700",
    color: "#1E293B",
  },
  availabilityText: {
    fontSize: 12,
    color: "#64748B",
  },
  statusBadge: {
    flexDirection: "row",
    alignItems: "center",
    gap: 6,
    backgroundColor: "#F0FDF4",
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: "#BBF7D0",
  },
  statusDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: "#22C55E",
  },
  statusText: {
    fontSize: 12,
    fontWeight: "600",
    color: "#15803D",
  },
  slotsSection: {
    marginBottom: 20,
  },
  slotsTitle: {
    fontSize: 16,
    fontWeight: "700",
    color: "#1E293B",
    marginBottom: 12,
  },
  slotsScroll: {
    marginHorizontal: -20,
    paddingHorizontal: 20,
  },
  slotCard: {
    width: 90,
    height: 90,
    backgroundColor: "rgba(255, 255, 255, 0.9)",
    borderRadius: 16,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 10,
    borderWidth: 2,
    borderColor: "rgba(203, 213, 225, 0.5)",
    position: "relative",
  },
  slotCardSelected: {
    borderColor: "#3B82F6",
    backgroundColor: "#EFF6FF",
  },
  slotCardDisabled: {
    opacity: 0.5,
    backgroundColor: "#F1F5F9",
  },
  slotCardPressed: {
    transform: [{ scale: 0.95 }],
  },
  selectedCheck: {
    position: "absolute",
    top: 6,
    right: 6,
  },
  slotDate: {
    fontSize: 13,
    fontWeight: "600",
    color: "#64748B",
    marginBottom: 4,
  },
  slotDateSelected: {
    color: "#3B82F6",
  },
  slotTime: {
    fontSize: 18,
    fontWeight: "800",
    color: "#1E293B",
  },
  slotTimeSelected: {
    color: "#2563EB",
  },
  slotTextDisabled: {
    color: "#CBD5E1",
  },
  unavailableOverlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: "rgba(241, 245, 249, 0.8)",
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 14,
  },
  unavailableText: {
    fontSize: 12,
    fontWeight: "700",
    color: "#94A3B8",
  },
  actions: {
    flexDirection: "row",
    gap: 10,
  },
  secondaryBtn: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    gap: 6,
    height: 52,
    backgroundColor: "rgba(255, 255, 255, 0.9)",
    borderRadius: 16,
    borderWidth: 1.5,
    borderColor: "rgba(203, 213, 225, 0.5)",
  },
  secondaryBtnText: {
    fontSize: 15,
    fontWeight: "700",
    color: "#64748B",
  },
  primaryBtn: {
    flex: 1.2,
    height: 52,
    borderRadius: 16,
    overflow: "hidden",
    ...Platform.select({
      ios: {
        shadowColor: "#3B82F6",
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.2,
        shadowRadius: 8,
      },
      android: {
        elevation: 4,
      },
    }),
  },
  primaryBtnDisabled: {
    opacity: 0.6,
  },
  primaryBtnGradient: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    gap: 6,
    height: "100%",
  },
  primaryBtnText: {
    fontSize: 16,
    fontWeight: "800",
    color: "#fff",
  },
});