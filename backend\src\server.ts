import Fastify, {
  FastifyInstance,
  FastifyRequest,
  FastifyReply,
} from "fastify";
import cors from "@fastify/cors";
import helmet from "@fastify/helmet";
import rateLimit from "@fastify/rate-limit";
import { randomUUID } from "crypto";
import { z } from "zod";

import {
  connectToDatabase,
  getUsersCollection,
  isDatabaseHealthy,
  closeDatabaseConnection,
} from "./database.js";
import {
  RegistrationCompleteSchema,
  UserListQuerySchema,
  RegIdParamSchema,
  validateAndNormalizeRegistrationStart,
} from "./schemas.js";
import type {
  User,
  RegistrationStartResponse,
  RegistrationCompleteResponse,
  UserListResponse,
  UserListItem,
} from "./types.js";

// Load environment variables
import { config } from "dotenv";
config();

const PORT = Number(process.env.PORT) || 3000;
const HOST = "0.0.0.0";
const NODE_ENV = process.env.NODE_ENV || "development";
const DEV_ADMIN_TOKEN = process.env.DEV_ADMIN_TOKEN || "dev-only-secret";

/**
 * Create and configure Fastify server
 */
async function createServer(): Promise<FastifyInstance> {
  const app = Fastify({
    logger: {
      level: process.env.LOG_LEVEL || "info",
      transport:
        NODE_ENV === "development"
          ? {
              target: "pino-pretty",
              options: {
                colorize: true,
                translateTime: "HH:MM:ss Z",
                ignore: "pid,hostname",
              },
            }
          : undefined,
    },
  });

  // Register plugins
  await app.register(helmet, {
    contentSecurityPolicy: NODE_ENV === "production",
  });

  await app.register(cors, {
  origin: true, // Allow all origins in development
  credentials: true,
});

  await app.register(rateLimit, {
    max: 100,
    timeWindow: "1 minute",
    errorResponseBuilder: (request, context) => ({
      code: 429,
      error: "Rate limit exceeded",
      message: `Too many requests, try again later`,
      date: Date.now(),
      expiresIn: Math.round(context.ttl / 1000),
    }),
  });

  // Health check endpoint
  app.get("/healthz", async (request, reply) => {
    const dbHealthy = await isDatabaseHealthy();

    if (dbHealthy) {
      return reply.code(200).send({
        status: "healthy",
        timestamp: new Date().toISOString(),
        database: "connected",
      });
    } else {
      return reply.code(503).send({
        status: "unhealthy",
        timestamp: new Date().toISOString(),
        database: "disconnected",
      });
    }
  });

  // API Routes
  await registerApiRoutes(app);

  return app;
}

/**
 * Register all API routes
 */
async function registerApiRoutes(app: FastifyInstance): Promise<void> {
  const usersCollection = getUsersCollection();

  // POST /api/registration/start
  app.post(
    "/api/registration/start",
    {
      preHandler: app.rateLimit({
        max: 10,
        timeWindow: "1 minute",
      }),
    },
    async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        // Validate and normalize input
        const validatedData = validateAndNormalizeRegistrationStart(
          request.body,
        );

        const now = new Date();
        const regId = randomUUID();

        const newUser: User = {
          _id: regId,
          fullName: validatedData.fullName,
          phone: validatedData.phone,
          email: validatedData.email,
          deviceId: validatedData.deviceId,
          status: "PENDING",
          regStage: 1,
          createdAt: now,
          updatedAt: now,
        };

        try {
          await usersCollection.insertOne(newUser);

          app.log.info(
            { regId, phone: validatedData.phone },
            "New registration started",
          );

          const response: RegistrationStartResponse = {
            regId,
            status: "PENDING",
          };

          return reply.code(201).send(response);
        } catch (error: any) {
          if (error.code === 11000) {
            // Duplicate phone number
            return reply.code(409).send({
              error: "Phone number already registered",
              code: "DUPLICATE_PHONE",
            });
          }
          throw error;
        }
      } catch (error) {
        app.log.error(error, "Error in registration start");

        if (error instanceof z.ZodError) {
          return reply.code(400).send({
            error: "Validation failed",
            details: error.errors,
          });
        }

        return reply.code(500).send({
          error: "Internal server error",
        });
      }
    },
  );

  // PATCH /api/registration/:regId/complete
  app.patch(
    "/api/registration/:regId/complete",
    async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        // Validate params
        const { regId } = RegIdParamSchema.parse(request.params);

        // Validate body
        const { address } = RegistrationCompleteSchema.parse(request.body);

        const result = await usersCollection.findOneAndUpdate(
          { _id: regId },
          {
            $set: {
              address,
              status: "COMPLETE",
              regStage: 2,
              updatedAt: new Date(),
            },
          },
          { returnDocument: "after" },
        );

        if (!result) {
          return reply.code(404).send({
            error: "Registration not found",
          });
        }

        app.log.info({ regId }, "Registration completed");

        const response: RegistrationCompleteResponse = {
          status: "COMPLETE",
          userId: result._id,
          createdAt: result.createdAt.toISOString(),
        };

        return reply.code(200).send(response);
      } catch (error) {
        app.log.error(error, "Error in registration complete");

        if (error instanceof z.ZodError) {
          return reply.code(400).send({
            error: "Validation failed",
            details: error.errors,
          });
        }

        return reply.code(500).send({
          error: "Internal server error",
        });
      }
    },
  );

  // GET /api/registration/:regId
  app.get(
    "/api/registration/:regId",
    async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const { regId } = RegIdParamSchema.parse(request.params);

        const user = await usersCollection.findOne({ _id: regId });

        if (!user) {
          return reply.code(404).send({
            error: "Registration not found",
          });
        }

        return reply.code(200).send(user);
      } catch (error) {
        app.log.error(error, "Error fetching registration");

        if (error instanceof z.ZodError) {
          return reply.code(400).send({
            error: "Invalid registration ID",
          });
        }

        return reply.code(500).send({
          error: "Internal server error",
        });
      }
    },
  );

  // POST /api/auth/login
  app.post(
    "/api/auth/login",
    {
      preHandler: app.rateLimit({
        max: 5,
        timeWindow: "1 minute",
      }),
    },
    async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const body = request.body as any;
        
        if (!body?.phone) {
          return reply.code(400).send({
            error: "Phone number is required",
          });
        }

        const phone = String(body.phone).trim();
        
        // Find user by phone number
        const user = await usersCollection.findOne({ 
          phone: phone,
          status: "COMPLETE" // Only allow login for completed registrations
        });

        if (!user) {
          app.log.info({ phone: phone.replace(/\d/g, '*') }, "Login attempt - user not found");
          return reply.code(404).send({
            error: "User not found with this phone number",
            code: "USER_NOT_FOUND",
          });
        }

        app.log.info({ 
          userId: user._id, 
          phone: phone.replace(/\d/g, '*') 
        }, "User logged in successfully");

        // Return user data (excluding sensitive fields)
        const userData = {
          _id: user._id,
          fullName: user.fullName,
          phone: user.phone,
          email: user.email,
          address: user.address,
          createdAt: user.createdAt.toISOString(),
        };

        return reply.code(200).send({
          user: userData,
          message: "Login successful",
        });
      } catch (error) {
        app.log.error(error, "Error in login");
        return reply.code(500).send({
          error: "Internal server error",
        });
      }
    },
  );

  // GET /api/users (Dev only)
  app.get(
    "/api/users",
    async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        // Check admin token
        const adminToken = request.headers["x-admin-token"];
        if (adminToken !== DEV_ADMIN_TOKEN) {
          return reply.code(401).send({
            error: "Unauthorized",
          });
        }

        // Validate query parameters
        const query = UserListQuerySchema.parse(request.query);
        const { q, limit = "20", cursor } = query;

        const pageSize = Math.min(parseInt(limit), 50);

        // Build MongoDB query
        const filters: any = {};

        if (q) {
          filters.$or = [
            { fullName: { $regex: q, $options: "i" } },
            { phone: { $regex: q, $options: "i" } },
            { email: { $regex: q, $options: "i" } },
            { "address.city": { $regex: q, $options: "i" } },
          ];
        }

        // Add cursor-based pagination
        if (cursor) {
          filters.createdAt = { $lt: new Date(cursor) };
        }

        const users = await usersCollection
          .find(filters)
          .sort({ createdAt: -1 })
          .limit(pageSize)
          .toArray();

        // Transform to response format
        const items: UserListItem[] = users.map((user) => ({
          _id: user._id,
          fullName: user.fullName,
          phone: user.phone,
          email: user.email,
          city: user.address?.city,
          createdAt: user.createdAt.toISOString(),
        }));

        const nextCursor =
          items.length === pageSize
            ? users[users.length - 1].createdAt.toISOString()
            : null;

        const response: UserListResponse = {
          items,
          nextCursor,
        };

        return reply.code(200).send(response);
      } catch (error) {
        app.log.error(error, "Error fetching users list");

        if (error instanceof z.ZodError) {
          return reply.code(400).send({
            error: "Invalid query parameters",
            details: error.errors,
          });
        }

        return reply.code(500).send({
          error: "Internal server error",
        });
      }
    },
  );
}

/**
 * Start the server
 */
async function startServer(): Promise<void> {
  try {
    // Connect to database
    await connectToDatabase();

    // Create and start server
    const app = await createServer();

    await app.listen({ port: PORT, host: HOST });

    console.log(`🚀 Server running on http://${HOST}:${PORT}`);
    console.log(`📊 Health check: http://${HOST}:${PORT}/healthz`);
    console.log(`📋 Environment: ${NODE_ENV}`);

    // Graceful shutdown
    const gracefulShutdown = async (signal: string) => {
      console.log(`\n🛑 Received ${signal}. Shutting down gracefully...`);

      try {
        await app.close();
        await closeDatabaseConnection();
        console.log("✅ Server shut down successfully");
        process.exit(0);
      } catch (error) {
        console.error("❌ Error during shutdown:", error);
        process.exit(1);
      }
    };

    process.on("SIGTERM", () => gracefulShutdown("SIGTERM"));
    process.on("SIGINT", () => gracefulShutdown("SIGINT"));
  } catch (error) {
    console.error("❌ Failed to start server:", error);
    process.exit(1);
  }
}

// Start the server if this file is run directly
const isMainModule = process.argv[1]?.endsWith('server.ts') || process.argv[1]?.endsWith('server.js');
if (isMainModule) {
  console.log("🚀 Starting server...");
  startServer();
}

export { createServer, startServer };
