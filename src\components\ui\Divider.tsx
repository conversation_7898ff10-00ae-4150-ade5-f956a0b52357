import React from 'react';
import { View, StyleSheet, ViewStyle } from 'react-native';

interface DividerProps {
  orientation?: 'horizontal' | 'vertical';
  thickness?: 'thin' | 'medium' | 'thick';
  color?: string;
  spacing?: 'none' | 'small' | 'medium' | 'large';
  style?: ViewStyle;
}

export const Divider: React.FC<DividerProps> = ({
  orientation = 'horizontal',
  thickness = 'thin',
  color = '#E5E7EB',
  spacing = 'medium',
  style,
}) => {
  return (
    <View style={[
      styles.divider,
      orientation === 'horizontal' ? styles.horizontal : styles.vertical,
      thicknessStyles[thickness][orientation],
      spacingStyles[spacing][orientation],
      { backgroundColor: color },
      style,
    ]} />
  );
};

const styles = StyleSheet.create({
  divider: {},
  horizontal: {
    width: '100%',
  },
  vertical: {
    height: '100%',
  },
});

const thicknessStyles = {
  thin: {
    horizontal: { height: StyleSheet.hairlineWidth },
    vertical: { width: StyleSheet.hairlineWidth },
  },
  medium: {
    horizontal: { height: 1 },
    vertical: { width: 1 },
  },
  thick: {
    horizontal: { height: 2 },
    vertical: { width: 2 },
  },
};

const spacingStyles = {
  none: {
    horizontal: { marginVertical: 0 },
    vertical: { marginHorizontal: 0 },
  },
  small: {
    horizontal: { marginVertical: 8 },
    vertical: { marginHorizontal: 8 },
  },
  medium: {
    horizontal: { marginVertical: 16 },
    vertical: { marginHorizontal: 16 },
  },
  large: {
    horizontal: { marginVertical: 24 },
    vertical: { marginHorizontal: 24 },
  },
};