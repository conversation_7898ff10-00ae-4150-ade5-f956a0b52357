// src/features/profile/components/ProfileHeader.tsx
import React, { useState } from 'react';
import {
  View,
  Text,
  Pressable,
  StyleSheet,
  Image,
  Animated,
  Platform,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { Ionicons } from '@expo/vector-icons';
import { EdgeInsets } from 'react-native-safe-area-context';
import * as ImagePicker from 'expo-image-picker';
import * as Haptics from 'expo-haptics';
import { tokens } from '@/src/core/theme/tokens';

interface ProfileHeaderProps {
  insets: EdgeInsets;
  scrollY: Animated.Value;
  user: {
    fullName: string;
    email: string;
    profileImage?: string | null;
  };
  onBack: () => void;
  onImageChange: (uri: string) => void;
}

export function ProfileHeader({ insets, scrollY, user, onBack, onImageChange }: ProfileHeaderProps) {
  const HEADER_MAX_HEIGHT = 280;
  const HEADER_MIN_HEIGHT = 100;
  const HEADER_SCROLL_DISTANCE = HEADER_MAX_HEIGHT - HEADER_MIN_HEIGHT;

  const pickImage = async () => {
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [1, 1],
      quality: 0.8,
    });

    if (!result.canceled) {
      onImageChange(result.assets[0].uri);
    }
  };

  return (
    <Animated.View
      style={[
        styles.container,
        {
          height: scrollY.interpolate({
            inputRange: [0, HEADER_SCROLL_DISTANCE],
            outputRange: [HEADER_MAX_HEIGHT + insets.top, HEADER_MIN_HEIGHT + insets.top],
            extrapolate: 'clamp',
          }),
        },
      ]}
    >
      <LinearGradient
        colors={['#F0F4FF', '#FFFFFF']}
        style={[styles.gradient, { paddingTop: insets.top }]}
      >
        {/* Animated background shapes */}
        <Animated.View
          style={[
            styles.backgroundShape,
            {
              opacity: scrollY.interpolate({
                inputRange: [0, HEADER_SCROLL_DISTANCE * 0.5],
                outputRange: [0.15, 0],
                extrapolate: 'clamp',
              }),
              transform: [
                {
                  scale: scrollY.interpolate({
                    inputRange: [0, HEADER_SCROLL_DISTANCE],
                    outputRange: [1, 1.3],
                    extrapolate: 'clamp',
                  }),
                },
              ],
            },
          ]}
        />

        {/* Navigation Bar */}
        <View style={styles.navBar}>
          <Pressable
            onPress={onBack}
            style={({ pressed }) => [
              styles.backButton,
              pressed && styles.pressed,
            ]}
          >
            <BlurView intensity={30} tint="light" style={styles.backButtonBlur}>
              <Ionicons name="arrow-back" size={22} color={tokens.colors.primary} />
            </BlurView>
          </Pressable>
          
          <Animated.Text
            style={[
              styles.title,
              {
                opacity: scrollY.interpolate({
                  inputRange: [0, HEADER_SCROLL_DISTANCE * 0.8],
                  outputRange: [0, 1],
                  extrapolate: 'clamp',
                }),
              },
            ]}
          >
            פרופיל
          </Animated.Text>
          
          <View style={{ width: 44 }} />
        </View>

        {/* Profile Content */}
        <Animated.View
          style={[
            styles.profileContent,
            {
              opacity: scrollY.interpolate({
                inputRange: [0, HEADER_SCROLL_DISTANCE * 0.7],
                outputRange: [1, 0],
                extrapolate: 'clamp',
              }),
              transform: [
                {
                  translateY: scrollY.interpolate({
                    inputRange: [0, HEADER_SCROLL_DISTANCE],
                    outputRange: [0, -20],
                    extrapolate: 'clamp',
                  }),
                },
                {
                  scale: scrollY.interpolate({
                    inputRange: [0, HEADER_SCROLL_DISTANCE],
                    outputRange: [1, 0.85],
                    extrapolate: 'clamp',
                  }),
                },
              ],
            },
          ]}
        >
          <Pressable onPress={pickImage} style={styles.imageContainer}>
            {user.profileImage ? (
              <Image source={{ uri: user.profileImage }} style={styles.profileImage} />
            ) : (
              <LinearGradient
                colors={['#6EA8FF', '#445EFB']}
                style={styles.placeholderImage}
              >
                <Text style={styles.placeholderText}>
                  {user.fullName?.charAt(0) || '?'}
                </Text>
              </LinearGradient>
            )}
            <View style={styles.editBadge}>
              <Ionicons name="camera" size={16} color="#FFFFFF" />
            </View>
          </Pressable>
          
          <Text style={styles.userName}>{user.fullName}</Text>
          <Text style={styles.userEmail}>{user.email}</Text>
        </Animated.View>
      </LinearGradient>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 10,
    overflow: 'hidden',
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.1,
        shadowRadius: 12,
      },
      android: {
        elevation: 8,
      },
    }),
  },
  gradient: {
    flex: 1,
  },
  backgroundShape: {
    position: 'absolute',
    top: -100,
    right: -100,
    width: 300,
    height: 300,
    borderRadius: 150,
    backgroundColor: tokens.colors.primary,
  },
  navBar: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 12,
  },
  backButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    overflow: 'hidden',
  },
  backButtonBlur: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.7)',
  },
  pressed: {
    opacity: 0.7,
    transform: [{ scale: 0.95 }],
  },
  title: {
    fontSize: 18,
    fontWeight: '700',
    color: tokens.colors.textPrimary,
  },
  profileContent: {
    alignItems: 'center',
    paddingTop: 20,
    paddingBottom: 30,
  },
  imageContainer: {
    position: 'relative',
    marginBottom: 16,
  },
  profileImage: {
    width: 110,
    height: 110,
    borderRadius: 55,
    borderWidth: 3,
    borderColor: '#FFFFFF',
  },
  placeholderImage: {
    width: 110,
    height: 110,
    borderRadius: 55,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: '#FFFFFF',
  },
  placeholderText: {
    fontSize: 42,
    fontWeight: '800',
    color: '#FFFFFF',
  },
  editBadge: {
    position: 'absolute',
    bottom: 5,
    right: 5,
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: tokens.colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: '#FFFFFF',
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.15,
        shadowRadius: 4,
      },
      android: {
        elevation: 4,
      },
    }),
  },
  userName: {
    fontSize: 26,
    fontWeight: '800',
    color: tokens.colors.textPrimary,
    marginBottom: 6,
  },
  userEmail: {
    fontSize: 16,
    color: tokens.colors.textSecondary,
  },
});