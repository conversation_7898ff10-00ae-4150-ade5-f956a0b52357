// src/features/profile/components/LogoutBlock.tsx
import React from 'react';
import { StyleSheet } from 'react-native';
import { Button } from '@/src/components';

interface Props {
  onLogout: () => void;
}

export function LogoutBlock({ onLogout }: Props) {
  return (
    <Button
      variant="danger"
      size="large"
      fullWidth
      icon="log-out-outline"
      style={styles.button}
      onPress={onLogout}
    >
      התנתק
    </Button>
  );
}

const styles = StyleSheet.create({
  button: {
    marginTop: 12,
  },
});