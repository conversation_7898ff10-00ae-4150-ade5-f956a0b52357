import { Stack, useRouter } from "expo-router";
import React, { useEffect, useRef, useState } from "react";
import {
  Animated,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  StatusBar,
  ScrollView,
} from "react-native";
import { BlurView } from "expo-blur";
import { LinearGradient } from "expo-linear-gradient";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { Ionicons, MaterialIcons } from "@expo/vector-icons";
import * as Haptics from "expo-haptics";
import { UserManager } from "../../services/api";

export default function NotConnected() {
  const router = useRouter();
  const insets = useSafeAreaInsets();
  const [user, setUser] = useState<any>(null);
  
  // Animation values
  const iconScale = useRef(new Animated.Value(0)).current;
  const contentOpacity = useRef(new Animated.Value(0)).current;
  const cardTranslateY = useRef(new Animated.Value(30)).current;
  const buttonScale = useRef(new Animated.Value(0.9)).current;

  useEffect(() => {
    // Load user data
    const loadUser = async () => {
      try {
        const userData = await UserManager.getUser();
        setUser(userData);
      } catch (error) {
        console.error('Failed to load user data:', error);
      }
    };

    loadUser();

    // Entrance animations
    Animated.sequence([
      Animated.parallel([
        Animated.spring(iconScale, {
          toValue: 1,
          tension: 30,
          friction: 7,
          useNativeDriver: true,
        }),
        Animated.timing(contentOpacity, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
      ]),
      Animated.parallel([
        Animated.spring(cardTranslateY, {
          toValue: 0,
          tension: 30,
          friction: 8,
          useNativeDriver: true,
        }),
        Animated.spring(buttonScale, {
          toValue: 1,
          tension: 30,
          friction: 7,
          useNativeDriver: true,
        }),
      ]),
    ]).start();
  }, []);

  const handleHowItWorks = async () => {
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    // Show modal or navigate to explanation
  };

  const handleContactSupport = async () => {
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    // Open support contact
  };

  const handleDevSimulate = async () => {
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    // Simulate enrollment and navigate to dashboard
    router.replace("/(main)/dashboard");
  };

  return (
    <>
      <Stack.Screen options={{ headerShown: false }} />
      <StatusBar barStyle="dark-content" />
      
      <View style={styles.container}>
        {/* Header */}
        <LinearGradient
          colors={["#F9FAFB", "#F3F4F6"]}
          style={[styles.header, { paddingTop: insets.top + 20 }]}
        >
          <Animated.View
            style={[
              styles.iconContainer,
              {
                opacity: contentOpacity,
                transform: [{ scale: iconScale }],
              },
            ]}
          >
            <View style={styles.iconWrapper}>
              <LinearGradient
                colors={["#FEF3C7", "#FDE68A"]}
                style={styles.iconGradient}
              >
                <MaterialIcons name="person-add-disabled" size={48} color="#F59E0B" />
              </LinearGradient>
            </View>
          </Animated.View>

          <Animated.View style={{ opacity: contentOpacity }}>
            <Text style={styles.title}>אתה עדיין לא מחובר למורה</Text>
            <Text style={styles.titleEn}>You're not connected to an instructor yet</Text>
          </Animated.View>
        </LinearGradient>

        {/* Content */}
        <ScrollView
          contentContainerStyle={styles.content}
          showsVerticalScrollIndicator={false}
        >
          <Animated.View
            style={[
              styles.card,
              {
                opacity: contentOpacity,
                transform: [{ translateY: cardTranslateY }],
              },
            ]}
          >
            <View style={styles.cardHeader}>
              <Ionicons name="information-circle" size={24} color="#445EFB" />
              <Text style={styles.cardTitle}>מה עכשיו?</Text>
            </View>
            
            <Text style={styles.description}>
              כדי להשתמש באפליקציה ולקבוע שיעורים, המורה שלך צריך להוסיף אותך למערכת.
            </Text>
            
            <Text style={styles.descriptionEn}>
              To use the app and schedule lessons, your instructor needs to add you to their system.
            </Text>

            <View style={styles.divider} />

            <Text style={styles.instructionTitle}>פנה למורה הנהיגה שלך ובקש ממנו להוסיף אותך למערכת שלו באמצעות:</Text>
            
            <View style={styles.infoContainer}>
              <View style={styles.infoRow}>
                <View style={styles.infoIcon}>
                  <Ionicons name="person" size={18} color="#6B7280" />
                </View>
                <View style={styles.infoContent}>
                  <Text style={styles.infoLabel}>השם המלא שלך</Text>
                  <Text style={styles.infoValue}>{user?.fullName || "טוען..."}</Text>
                </View>
              </View>
              
              <View style={styles.infoRow}>
                <View style={styles.infoIcon}>
                  <Ionicons name="phone" size={18} color="#6B7280" />
                </View>
                <View style={styles.infoContent}>
                  <Text style={styles.infoLabel}>מספר הטלפון שלך</Text>
                  <Text style={styles.infoValue}>{user?.phone || "טוען..."}</Text>
                </View>
              </View>
            </View>
          </Animated.View>
        </ScrollView>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F9FAFB",
  },
  header: {
    alignItems: "center",
    paddingHorizontal: 20,
    paddingBottom: 30,
  },
  iconContainer: {
    marginBottom: 20,
  },
  iconWrapper: {
    width: 80,
    height: 80,
    borderRadius: 40,
    padding: 2,
    backgroundColor: "#FFFFFF",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  iconGradient: {
    width: "100%",
    height: "100%",
    borderRadius: 38,
    alignItems: "center",
    justifyContent: "center",
  },
  title: {
    fontSize: 20,
    fontWeight: "600",
    color: "#1F2937",
    textAlign: "center",
    marginBottom: 8,
  },
  titleEn: {
    fontSize: 16,
    color: "#6B7280",
    textAlign: "center",
  },
  content: {
    padding: 20,
  },
  card: {
    backgroundColor: "#FFFFFF",
    borderRadius: 16,
    padding: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  cardHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#1F2937",
    marginLeft: 8,
  },
  description: {
    fontSize: 16,
    color: "#374151",
    lineHeight: 24,
    textAlign: "right",
    marginBottom: 12,
  },
  descriptionEn: {
    fontSize: 14,
    color: "#6B7280",
    lineHeight: 20,
    textAlign: "left",
    marginBottom: 20,
  },
  divider: {
    height: 1,
    backgroundColor: "#E5E7EB",
    marginVertical: 20,
  },
  instructionTitle: {
    fontSize: 16,
    fontWeight: "500",
    color: "#1F2937",
    textAlign: "right",
    marginBottom: 16,
  },
  infoContainer: {
    gap: 12,
  },
  infoRow: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#F9FAFB",
    padding: 12,
    borderRadius: 8,
  },
  infoIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: "#FFFFFF",
    alignItems: "center",
    justifyContent: "center",
    marginRight: 12,
  },
  infoContent: {
    flex: 1,
  },
  infoLabel: {
    fontSize: 12,
    color: "#6B7280",
    marginBottom: 2,
  },
  infoValue: {
    fontSize: 14,
    fontWeight: "500",
    color: "#1F2937",
  },
});