import { Stack, useRouter } from "expo-router";
import React, { useEffect, useRef, useState } from "react";
import {
  Animated,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  View,
  Keyboard,
  StatusBar,
  Pressable,
  Dimensions,
  ActivityIndicator,
} from "react-native";
import { BlurView } from "expo-blur";
import { LinearGradient } from "expo-linear-gradient";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { Ionicons } from "@expo/vector-icons";
import * as Haptics from "expo-haptics";
import { RegistrationAPI, RegistrationManager, APIError, APIUtils } from "../../services/api";

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get("window");

interface AddressData {
  city: string;
  street: string;
  buildingNumber: string;
  apartment: string;
}

// Simple local suggestions (kept from your file)
const ISRAELI_CITIES = [
  "תל אביב",
  "ירושלים",
  "חיפה",
  "רא<PERSON>ו<PERSON> לציון",
  "פתח תקווה",
  "אשדו<PERSON>",
  "נתניה",
  "באר שבע",
  "בני ברק",
  "חולון",
  "רמת גן",
  "אשקלון",
  "רחובות",
  "בת ים",
  "כפר סבא",
];

export default function SignupStep2() {
  const router = useRouter();
  const insets = useSafeAreaInsets();
  const scrollViewRef = useRef<ScrollView>(null);

  const [addressData, setAddressData] = useState<AddressData>({
    city: "",
    street: "",
    buildingNumber: "",
    apartment: "",
  });

  const [focusedField, setFocusedField] = useState<keyof AddressData | null>(null);
  const [errors, setErrors] = useState<Partial<AddressData>>({});
  const [citySuggestions, setCitySuggestions] = useState<string[]>([]);
  const [showCitySuggestions, setShowCitySuggestions] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [apiError, setApiError] = useState<string | null>(null);

  // Entrance / UI animation values (mirroring step1 style)
  const titleOpacity = useRef(new Animated.Value(0)).current;
  const titleTranslateY = useRef(new Animated.Value(20)).current;
  const progressScale = useRef(new Animated.Value(0)).current;
  const field1Opacity = useRef(new Animated.Value(0)).current;
  const field1TranslateX = useRef(new Animated.Value(-30)).current;
  const field2Opacity = useRef(new Animated.Value(0)).current;
  const field2TranslateX = useRef(new Animated.Value(-30)).current;
  const field3Opacity = useRef(new Animated.Value(0)).current;
  const field3TranslateX = useRef(new Animated.Value(-30)).current;
  const field4Opacity = useRef(new Animated.Value(0)).current;
  const field4TranslateX = useRef(new Animated.Value(-30)).current;
  const buttonTranslateY = useRef(new Animated.Value(100)).current;
  const buttonOpacity = useRef(new Animated.Value(0)).current;
  const blob1Translate = useRef(new Animated.Value(0)).current;
  const blob2Translate = useRef(new Animated.Value(0)).current;
  const backButtonScale = useRef(new Animated.Value(0)).current;
  const successScale = useRef(new Animated.Value(0)).current;
  const successOpacity = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Entrance sequence with proper cascade (faster than original, but maintaining order)
    Animated.sequence([
      Animated.spring(backButtonScale, {
        toValue: 1,
        tension: 30,
        friction: 8,
        useNativeDriver: true,
      }),
      Animated.parallel([
        Animated.timing(titleOpacity, {
          toValue: 1,
          duration: 500,
          useNativeDriver: true,
        }),
        Animated.spring(titleTranslateY, {
          toValue: 0,
          tension: 30,
          friction: 8,
          useNativeDriver: true,
        }),
        Animated.spring(progressScale, {
          toValue: 1,
          tension: 20,
          friction: 7,
          delay: 100,
          useNativeDriver: true,
        }),
      ]),
      Animated.stagger(100, [
        Animated.parallel([
          Animated.timing(field1Opacity, { toValue: 1, duration: 450, useNativeDriver: true }),
          Animated.spring(field1TranslateX, { toValue: 0, tension: 30, friction: 8, useNativeDriver: true }),
        ]),
        Animated.parallel([
          Animated.timing(field2Opacity, { toValue: 1, duration: 450, useNativeDriver: true }),
          Animated.spring(field2TranslateX, { toValue: 0, tension: 30, friction: 8, useNativeDriver: true }),
        ]),
        Animated.parallel([
          Animated.timing(field3Opacity, { toValue: 1, duration: 450, useNativeDriver: true }),
          Animated.spring(field3TranslateX, { toValue: 0, tension: 30, friction: 8, useNativeDriver: true }),
        ]),
        Animated.parallel([
          Animated.timing(field4Opacity, { toValue: 1, duration: 450, useNativeDriver: true }),
          Animated.spring(field4TranslateX, { toValue: 0, tension: 30, friction: 8, useNativeDriver: true }),
        ]),
      ]),
      Animated.parallel([
        Animated.spring(buttonTranslateY, { toValue: 0, tension: 30, friction: 8, useNativeDriver: true }),
        Animated.timing(buttonOpacity, { toValue: 1, duration: 500, useNativeDriver: true }),
      ]),
    ]).start();

    // Gentle floating blobs
    const blob1Animation = Animated.loop(
      Animated.sequence([
        Animated.timing(blob1Translate, { toValue: 25, duration: 3500, useNativeDriver: true }),
        Animated.timing(blob1Translate, { toValue: -25, duration: 3500, useNativeDriver: true }),
      ])
    );
    const blob2Animation = Animated.loop(
      Animated.sequence([
        Animated.timing(blob2Translate, { toValue: -20, duration: 4000, useNativeDriver: true }),
        Animated.timing(blob2Translate, { toValue: 20, duration: 4000, useNativeDriver: true }),
      ])
    );
    blob1Animation.start();
    blob2Animation.start();
    return () => {
      blob1Animation.stop();
      blob2Animation.stop();
    };
  }, []);

  const handleBack = async () => {
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    router.back();
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<AddressData> = {};
    if (!addressData.city.trim()) newErrors.city = "עיר נדרשת";
    if (!addressData.street.trim()) newErrors.street = "רחוב נדרש";
    if (!addressData.buildingNumber.trim()) newErrors.buildingNumber = "מספר בית נדרש";
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const isFormValid =
    addressData.city.trim() && addressData.street.trim() && addressData.buildingNumber.trim() && !isLoading;

  const handleComplete = async () => {
    if (!validateForm()) {
      await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      return;
    }
    
    setIsLoading(true);
    setApiError(null);
    
    try {
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      Keyboard.dismiss();

      // Call the complete registration API
      const response = await RegistrationAPI.completeRegistration({
        city: addressData.city,
        street: addressData.street,
        buildingNumber: addressData.buildingNumber,
        apartment: addressData.apartment || undefined,
      });

      // Clear regId since registration is complete
      await RegistrationManager.clearRegId();

      // Success splash
      setShowSuccess(true);
      Animated.parallel([
        Animated.spring(successScale, { toValue: 1, tension: 30, friction: 7, useNativeDriver: true }),
        Animated.timing(successOpacity, { toValue: 1, duration: 400, useNativeDriver: true }),
      ]).start();

      await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      setTimeout(() => {
        router.push("/(auth)/not-connected");
      }, 1200);
      
    } catch (error) {
      await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      
      let errorMessage = 'שגיאה בסיום הרשמה. נסה שוב.';
      
      if (error instanceof APIError) {
        if (error.status === 404) {
          errorMessage = 'מזהה הרשמה לא נמצא. התחל הרשמה מחדש.';
          // Navigate back to step 1 after a delay
          setTimeout(() => {
            router.push("/(auth)/signup-step1");
          }, 2000);
        } else if (APIUtils.isNetworkError(error)) {
          errorMessage = 'בעיית רשת. בדוק חיבור אינטרנט ונסה שוב.';
        } else {
          errorMessage = error.message;
        }
      }
      
      setApiError(errorMessage);
      console.error('Registration completion error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const onCityChange = (text: string) => {
    setAddressData({ ...addressData, city: text });
    if (text.length > 0) {
      const filtered = ISRAELI_CITIES.filter((c) =>
        c.toLowerCase().includes(text.toLowerCase())
      );
      setCitySuggestions(filtered);
      setShowCitySuggestions(filtered.length > 0);
    } else {
      setShowCitySuggestions(false);
    }
    if (errors.city) setErrors({ ...errors, city: undefined });
  };

  const selectCity = async (city: string) => {
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setAddressData({ ...addressData, city });
    setShowCitySuggestions(false);
    Keyboard.dismiss();
  };

  return (
    <>
      <Stack.Screen options={{ headerShown: false }} />
      <StatusBar barStyle="dark-content" />

      <LinearGradient
        colors={["#EDF3FF", "#EAF1FF", "#E7EEFF"]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.container}
      >
        {/* Floating background blobs */}
        <View style={styles.blobs} pointerEvents="none">
          <Animated.View
            style={[
              styles.blob,
              styles.blobA,
              { transform: [{ translateX: blob1Translate }, { translateY: Animated.multiply(blob1Translate, 0.3) }] },
            ]}
          />
          <Animated.View
            style={[
              styles.blob,
              styles.blobB,
              { transform: [{ translateX: blob2Translate }, { translateY: Animated.multiply(blob2Translate, -0.5) }] },
            ]}
          />
        </View>

        {/* Header (mirrors step1: back, dots, step badge) */}
        <View style={[styles.header, { paddingTop: insets.top + 10 }]}>
          <Animated.View style={{ transform: [{ scale: backButtonScale }] }}>
            <Pressable
              onPress={handleBack}
              style={({ pressed }) => [styles.backButton, { transform: [{ scale: pressed ? 0.9 : 1 }] }]}
            >
              <BlurView intensity={30} tint="light" style={styles.backButtonBlur}>
                <Ionicons name="chevron-back" size={24} color="#445EFB" />
              </BlurView>
            </Pressable>
          </Animated.View>

          <Animated.View style={[styles.progressDots, { transform: [{ scale: progressScale }] }]}>
            <View style={styles.dot} />
            <View style={[styles.dot, styles.dotActive]} />
          </Animated.View>

          <View style={styles.stepBadge}>
            <Text style={styles.stepText}>2/2</Text>
          </View>
        </View>

        {/* Title */}
        <Animated.View
          style={[
            styles.titleSection,
            { opacity: titleOpacity, transform: [{ translateY: titleTranslateY }] },
          ]}
        >
          <Text style={styles.titleKicker}>שלב שני</Text>
          <Text style={styles.title}>פרטי כתובת</Text>
          <Text style={styles.subtitle}>נשלים את הכתובת שלך</Text>
        </Animated.View>

        {/* Form */}
        <KeyboardAvoidingView
          behavior={Platform.OS === "ios" ? "padding" : "height"}
          style={styles.formContainer}
        >
          <ScrollView
            ref={scrollViewRef}
            contentContainerStyle={styles.scrollContent}
            showsVerticalScrollIndicator={false}
            keyboardShouldPersistTaps="handled"
          >
            {/* City */}
            <Animated.View
              style={[
                styles.inputGroup,
                { opacity: field1Opacity, transform: [{ translateX: field1TranslateX }] },
              ]}
            >
              <Text style={styles.label}>עיר</Text>
              <Pressable
                style={[
                  styles.inputContainer,
                  focusedField === "city" && styles.inputContainerFocused,
                  errors.city && styles.inputContainerError,
                ]}
              >
                <BlurView intensity={focusedField === "city" ? 0 : 15} tint="light" style={styles.inputBlur}>
                  <View style={styles.inputContent}>
                    <View
                      style={[
                        styles.inputIconWrapper,
                        focusedField === "city" && styles.inputIconWrapperActive,
                      ]}
                    >
                      <Ionicons
                        name="location"
                        size={18}
                        color={focusedField === "city" ? "#445EFB" : "#9CA3AF"}
                      />
                    </View>
                    <TextInput
                      value={addressData.city}
                      onChangeText={onCityChange}
                      onFocus={() => {
                        setFocusedField("city");
                        if (addressData.city.length === 0) {
                          setCitySuggestions(ISRAELI_CITIES.slice(0, 5));
                          setShowCitySuggestions(true);
                        }
                      }}
                      onBlur={() => {
                        setFocusedField(null);
                        setTimeout(() => setShowCitySuggestions(false), 200);
                      }}
                      placeholder="בחר/י עיר"
                      placeholderTextColor="#9CA3AF"
                      style={styles.input}
                      returnKeyType="next"
                    />
                  </View>
                </BlurView>
              </Pressable>
              {errors.city && <Animated.Text style={styles.errorText}>{errors.city}</Animated.Text>}

              {/* Suggestions as a glassy dropdown */}
              {showCitySuggestions && (
                <View style={styles.suggestionsWrapper}>
                  <BlurView intensity={20} tint="light" style={styles.suggestionsBlur}>
                    {citySuggestions.map((city, idx) => (
                      <Pressable
                        key={idx}
                        onPress={() => selectCity(city)}
                        style={({ pressed }) => [
                          styles.suggestionItem,
                          pressed && { opacity: 0.85 },
                        ]}
                      >
                        <Ionicons name="location" size={16} color="#445EFB" />
                        <Text style={styles.suggestionText}>{city}</Text>
                      </Pressable>
                    ))}
                  </BlurView>
                </View>
              )}
            </Animated.View>

            {/* Street */}
            <Animated.View
              style={[
                styles.inputGroup,
                { opacity: field2Opacity, transform: [{ translateX: field2TranslateX }] },
              ]}
            >
              <Text style={styles.label}>רחוב</Text>
              <Pressable
                style={[
                  styles.inputContainer,
                  focusedField === "street" && styles.inputContainerFocused,
                  errors.street && styles.inputContainerError,
                ]}
              >
                <BlurView intensity={focusedField === "street" ? 0 : 15} tint="light" style={styles.inputBlur}>
                  <View style={styles.inputContent}>
                    <View
                      style={[
                        styles.inputIconWrapper,
                        focusedField === "street" && styles.inputIconWrapperActive,
                      ]}
                    >
                      <Ionicons
                        name="map-outline"
                        size={18}
                        color={focusedField === "street" ? "#445EFB" : "#9CA3AF"}
                      />
                    </View>
                    <TextInput
                      value={addressData.street}
                      onChangeText={(text) => {
                        setAddressData({ ...addressData, street: text });
                        if (errors.street) setErrors({ ...errors, street: undefined });
                      }}
                      onFocus={() => setFocusedField("street")}
                      onBlur={() => setFocusedField(null)}
                      placeholder="שם הרחוב"
                      placeholderTextColor="#9CA3AF"
                      style={styles.input}
                      returnKeyType="next"
                    />
                  </View>
                </BlurView>
              </Pressable>
              {errors.street && <Animated.Text style={styles.errorText}>{errors.street}</Animated.Text>}
            </Animated.View>

            {/* Building + Apartment */}
            <Animated.View
              style={[
                styles.row,
                { opacity: field3Opacity, transform: [{ translateX: field3TranslateX }] },
              ]}
            >
              {/* Building number */}
              <View style={{ flex: 1 }}>
                <Text style={styles.label}>מס׳ בית</Text>
                <Pressable
                  style={[
                    styles.inputContainer,
                    focusedField === "buildingNumber" && styles.inputContainerFocused,
                    errors.buildingNumber && styles.inputContainerError,
                  ]}
                >
                  <BlurView
                    intensity={focusedField === "buildingNumber" ? 0 : 15}
                    tint="light"
                    style={styles.inputBlur}
                  >
                    <View style={styles.inputContent}>
                      <View
                        style={[
                          styles.inputIconWrapper,
                          focusedField === "buildingNumber" && styles.inputIconWrapperActive,
                        ]}
                      >
                        <Ionicons
                          name="home-outline"
                          size={18}
                          color={focusedField === "buildingNumber" ? "#445EFB" : "#9CA3AF"}
                        />
                      </View>
                      <TextInput
                        value={addressData.buildingNumber}
                        onChangeText={(text) => {
                          setAddressData({ ...addressData, buildingNumber: text });
                          if (errors.buildingNumber) setErrors({ ...errors, buildingNumber: undefined });
                        }}
                        onFocus={() => setFocusedField("buildingNumber")}
                        onBlur={() => setFocusedField(null)}
                        placeholder="לדוגמה 12"
                        placeholderTextColor="#9CA3AF"
                        style={styles.input}
                        keyboardType="number-pad"
                        returnKeyType="next"
                      />
                    </View>
                  </BlurView>
                </Pressable>
                {errors.buildingNumber && (
                  <Animated.Text style={styles.errorText}>{errors.buildingNumber}</Animated.Text>
                )}
              </View>

              {/* Apartment */}
              <View style={{ flex: 0.8 }}>
                <Text style={styles.label}>דירה</Text>
                <Pressable
                  style={[
                    styles.inputContainer,
                    focusedField === "apartment" && styles.inputContainerFocused,
                  ]}
                >
                  <BlurView
                    intensity={focusedField === "apartment" ? 0 : 15}
                    tint="light"
                    style={styles.inputBlur}
                  >
                    <View style={styles.inputContent}>
                      <View
                        style={[
                          styles.inputIconWrapper,
                          focusedField === "apartment" && styles.inputIconWrapperActive,
                        ]}
                      >
                        <Ionicons
                          name="apps-outline"
                          size={18}
                          color={focusedField === "apartment" ? "#445EFB" : "#9CA3AF"}
                        />
                      </View>
                      <TextInput
                        value={addressData.apartment}
                        onChangeText={(text) => setAddressData({ ...addressData, apartment: text })}
                        onFocus={() => setFocusedField("apartment")}
                        onBlur={() => setFocusedField(null)}
                        placeholder="לדוגמה 5"
                        placeholderTextColor="#9CA3AF"
                        style={[styles.input, { textAlign: "center" }]}
                        keyboardType="number-pad"
                        returnKeyType="done"
                      />
                    </View>
                  </BlurView>
                </Pressable>
              </View>
            </Animated.View>

            {/* Spacer to avoid overlap with bottom CTA */}
            <View style={{ height: 140 }} />
          </ScrollView>
        </KeyboardAvoidingView>

        {/* Bottom CTA (same component style as step1) */}
        <Animated.View
          style={[
            styles.bottomSection,
            { paddingBottom: insets.bottom + 24, opacity: buttonOpacity, transform: [{ translateY: buttonTranslateY }] },
          ]}
        >
          {/* API Error Display */}
          {apiError && (
            <View style={styles.errorContainer}>
              <Ionicons name="alert-circle" size={18} color="#EF4444" />
              <Text style={styles.errorMessage}>{apiError}</Text>
            </View>
          )}

          <Pressable
            onPress={handleComplete}
            disabled={!isFormValid}
            style={({ pressed }) => [
              styles.continueButton,
              !isFormValid && styles.continueButtonDisabled,
              { transform: [{ scale: pressed && isFormValid ? 0.97 : 1 }] },
            ]}
          >
            {({ pressed }) => (
              <LinearGradient
                colors={
                  isFormValid
                    ? pressed
                      ? ["#3A52E3", "#5E98F3"]
                      : ["#445EFB", "#6EA8FF"]
                    : ["#E5E7EB", "#E5E7EB"]
                }
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
                style={styles.continueButtonGradient}
              >
                {isLoading ? (
                  <ActivityIndicator color="#fff" size="small" />
                ) : (
                  <>
                    <Text
                      style={[styles.continueButtonText, !isFormValid && styles.continueButtonTextDisabled]}
                    >
                      סיום הרשמה
                    </Text>
                    <View
                      style={[styles.continueButtonArrow, !isFormValid && styles.continueButtonArrowDisabled]}
                    >
                      <Ionicons
                        name="checkmark"
                        size={20}
                        color={isFormValid ? "#fff" : "#9CA3AF"}
                      />
                    </View>
                  </>
                )}
              </LinearGradient>
            )}
          </Pressable>

          <Text style={styles.privacyNote}>המידע שלך מאובטח ולא ישותף עם צד שלישי</Text>
        </Animated.View>

        {/* Success overlay */}
        {showSuccess && (
          <Animated.View
            style={[
              styles.successOverlay,
              { opacity: successOpacity, transform: [{ scale: successScale }] },
            ]}
            pointerEvents="none"
          >
            <View style={styles.successCard}>
              <Ionicons name="checkmark-circle" size={64} color="#10B981" />
              <Text style={styles.successText}>נרשמת בהצלחה!</Text>
            </View>
          </Animated.View>
        )}
      </LinearGradient>
    </>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1 },

  // Blobs
  blobs: {
    position: "absolute",
    width: "100%",
    height: "100%",
  },
  blob: {
    position: "absolute",
    borderRadius: 999,
    backgroundColor: "#ffffff",
    ...Platform.select({
      ios: {
        shadowColor: "#4C6BFF",
        shadowOpacity: 0.1,
        shadowRadius: 40,
        shadowOffset: { width: 0, height: 10 },
      },
      android: { elevation: 0 },
    }),
  },
  blobA: {
    top: 60,
    right: -80,
    width: 200,
    height: 200,
    opacity: 0.35,
  },
  blobB: {
    bottom: 150,
    left: -100,
    width: 250,
    height: 250,
    opacity: 0.3,
  },

  // Header (back, dots, step)
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 22,
    paddingBottom: 20,
  },
  backButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    overflow: "hidden",
  },
  backButtonBlur: {
    width: "100%",
    height: "100%",
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(255, 255, 255, 0.75)",
    borderWidth: StyleSheet.hairlineWidth,
    borderColor: "rgba(0, 0, 0, 0.08)",
  },
  progressDots: {
    flexDirection: "row",
    gap: 8,
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: "rgba(68, 94, 251, 0.2)",
  },
  dotActive: {
    width: 24,
    backgroundColor: "#445EFB",
  },
  stepBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 14,
    backgroundColor: "rgba(68, 94, 251, 0.1)",
  },
  stepText: {
    fontSize: 12,
    fontWeight: "700",
    color: "#445EFB",
  },

  // Title section
  titleSection: {
    alignItems: "center",
    paddingHorizontal: 28,
    marginBottom: 32,
  },
  titleKicker: {
    fontSize: 13,
    fontWeight: "700",
    color: "#5873FF",
    letterSpacing: 1,
    marginBottom: 8,
  },
  title: {
    fontSize: 32,
    fontWeight: "800",
    color: "#0B0B0B",
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 15,
    color: "rgba(0, 0, 0, 0.6)",
  },

  // Form
  formContainer: { flex: 1 },
  scrollContent: {
    paddingHorizontal: 22,
    paddingBottom: 150,
  },
  inputGroup: { gap: 10, marginBottom: 10 },
  row: { flexDirection: "row", gap: 12 },

  label: {
    fontSize: 14,
    fontWeight: "700",
    color: "#0B0B0B",
    marginLeft: 4,
  },

  inputContainer: {
    height: 60,
    borderRadius: 16,
    overflow: "hidden",
    borderWidth: 1.5,
    borderColor: "transparent",
    backgroundColor: "rgba(255, 255, 255, 0.5)",
    ...Platform.select({
      ios: {
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.05,
        shadowRadius: 8,
      },
      android: { elevation: 2 },
    }),
  },
  inputContainerFocused: {
    borderColor: "#445EFB",
    backgroundColor: "#fff",
    ...Platform.select({
      ios: {
        shadowColor: "#445EFB",
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.15,
        shadowRadius: 12,
      },
      android: { elevation: 4 },
    }),
  },
  inputContainerError: {
    borderColor: "#EF4444",
  },
  inputBlur: { flex: 1 },
  inputContent: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
  },
  inputIconWrapper: {
    width: 32,
    height: 32,
    borderRadius: 10,
    backgroundColor: "rgba(68, 94, 251, 0.08)",
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  inputIconWrapperActive: {
    backgroundColor: "rgba(68, 94, 251, 0.15)",
  },
  input: {
    flex: 1,
    fontSize: 16,
    fontWeight: "500",
    color: "#0B0B0B",
  },
  errorText: {
    fontSize: 12,
    fontWeight: "600",
    color: "#EF4444",
    marginLeft: 4,
  },

  // Suggestions (glassy dropdown)
  suggestionsWrapper: {
    marginTop: 6,
    borderRadius: 14,
    overflow: "hidden",
  },
  suggestionsBlur: {
    paddingVertical: 6,
    backgroundColor: "rgba(255,255,255,0.7)",
    borderWidth: 1,
    borderColor: "rgba(0,0,0,0.06)",
  },
  suggestionItem: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
    paddingVertical: 10,
    paddingHorizontal: 12,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: "rgba(0,0,0,0.06)",
  },
  suggestionText: {
    fontSize: 15,
    color: "#374151",
  },

  // Bottom CTA (same as step1)
  bottomSection: {
    paddingHorizontal: 22,
  },
  errorContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
    backgroundColor: "rgba(239, 68, 68, 0.1)",
    borderWidth: 1,
    borderColor: "rgba(239, 68, 68, 0.2)",
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginBottom: 16,
  },
  errorMessage: {
    flex: 1,
    fontSize: 14,
    fontWeight: "600",
    color: "#EF4444",
    textAlign: "right",
  },
  continueButton: {
    borderRadius: 16,
    overflow: "hidden",
    marginBottom: 16,
    ...Platform.select({
      ios: {
        shadowColor: "#445EFB",
        shadowOpacity: 0.3,
        shadowRadius: 20,
        shadowOffset: { width: 0, height: 10 },
      },
      android: { elevation: 8 },
    }),
  },
  continueButtonDisabled: {
    ...Platform.select({
      ios: { shadowOpacity: 0.08, shadowColor: "#000" },
      android: { elevation: 2 },
    }),
  },
  continueButtonGradient: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 18,
    gap: 8,
  },
  continueButtonText: {
    fontSize: 17,
    fontWeight: "800",
    color: "#fff",
    letterSpacing: 0.3,
  },
  continueButtonTextDisabled: {
    color: "#9CA3AF",
  },
  continueButtonArrow: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: "rgba(255, 255, 255, 0.25)",
    alignItems: "center",
    justifyContent: "center",
  },
  continueButtonArrowDisabled: {
    backgroundColor: "rgba(0, 0, 0, 0.05)",
  },
  privacyNote: {
    textAlign: "center",
    fontSize: 12,
    fontWeight: "600",
    color: "rgba(0, 0, 0, 0.4)",
    letterSpacing: 0.3,
  },

  // Success overlay
  successOverlay: {
    position: "absolute",
    top: 0, left: 0, right: 0, bottom: 0,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0,0,0,0.25)",
  },
  successCard: {
    backgroundColor: "#fff",
    borderRadius: 24,
    padding: 32,
    alignItems: "center",
    gap: 16,
    ...Platform.select({
      ios: {
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 10 },
        shadowOpacity: 0.2,
        shadowRadius: 20,
      },
      android: { elevation: 10 },
    }),
  },
  successText: {
    fontSize: 20,
    fontWeight: "700",
    color: "#1F2937",
  },
});
