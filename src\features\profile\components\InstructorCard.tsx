// src/features/profile/components/InstructorCard.tsx
import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Card, Avatar, Button } from '@/src/components';
import { tokens } from '@/src/core/theme/tokens';
import { Instructor } from '../types';

interface Props {
  instructor: Instructor;
  onCall: () => void;
}

export function InstructorCard({ instructor, onCall }: Props) {
  return (
    <Card 
      variant="gradient" 
      gradientColors={['#EFF6FF', '#DBEAFE']} 
      style={styles.container}
    >
      <View style={styles.content}>
        <Avatar 
          name={instructor.name} 
          size="medium"
          source={instructor.profileImage ? { uri: instructor.profileImage } : undefined}
        />
        <View style={styles.info}>
          <Text style={styles.name}>{instructor.name}</Text>
          <Text style={styles.role}>המורה שלך</Text>
          <View style={styles.status}>
            <View style={[
              styles.statusDot,
              { backgroundColor: instructor.connected ? '#22C55E' : '#EF4444' }
            ]} />
            <Text style={styles.statusText}>
              {instructor.connected ? 'מחובר' : 'לא מחובר'}
            </Text>
          </View>
        </View>
        <Button
          variant="secondary"
          size="small"
          icon="call"
          onPress={onCall}
        >
          התקשר
        </Button>
      </View>
    </Card>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  info: {
    flex: 1,
  },
  name: {
    fontSize: 16,
    fontWeight: '700',
    color: tokens.colors.textPrimary,
  },
  role: {
    fontSize: 12,
    color: tokens.colors.textSecondary,
    marginTop: 2,
  },
  status: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    marginTop: 6,
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  statusText: {
    fontSize: 12,
    color: tokens.colors.textSecondary,
  },
});