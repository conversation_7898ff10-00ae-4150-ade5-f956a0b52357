import React from 'react';
import { View, Text, StyleSheet, Pressable } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';

interface TimePickerCardProps {
  time: string;
  date?: string;
  label?: string;
  selected?: boolean;
  available?: boolean;
  onPress?: () => void;
}

export const TimePickerCard: React.FC<TimePickerCardProps> = ({
  time,
  date,
  label,
  selected = false,
  available = true,
  onPress,
}) => {
  const handlePress = async () => {
    if (onPress && available) {
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      onPress();
    }
  };

  const renderContent = () => (
    <>
      {label && <Text style={styles.label}>{label}</Text>}
      <Text style={[
        styles.time,
        selected && styles.selectedText,
        !available && styles.unavailableText,
      ]}>
        {time}
      </Text>
      {date && (
        <Text style={[
          styles.date,
          selected && styles.selectedText,
          !available && styles.unavailableText,
        ]}>
          {date}
        </Text>
      )}
      {selected && (
        <View style={styles.checkIcon}>
          <Ionicons name="checkmark-circle" size={20} color="#FFFFFF" />
        </View>
      )}
    </>
  );

  if (selected) {
    return (
      <Pressable
        onPress={handlePress}
        disabled={!available}
        style={({ pressed }) => [
          styles.card,
          pressed && styles.pressed,
          !available && styles.unavailable,
        ]}
      >
        <LinearGradient
          colors={['#445EFB', '#6EA8FF']}
          style={styles.gradient}
        >
          {renderContent()}
        </LinearGradient>
      </Pressable>
    );
  }

  return (
    <Pressable
      onPress={handlePress}
      disabled={!available}
      style={({ pressed }) => [
        styles.card,
        styles.defaultCard,
        pressed && available && styles.pressed,
        !available && styles.unavailable,
      ]}
    >
      {renderContent()}
    </Pressable>
  );
};

const styles = StyleSheet.create({
  card: {
    minWidth: 100,
    minHeight: 80,
    borderRadius: 16,
    overflow: 'hidden',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 12,
  },
  defaultCard: {
    backgroundColor: '#FFFFFF',
    borderWidth: 1.5,
    borderColor: '#E5E7EB',
  },
  gradient: {
    flex: 1,
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 12,
  },
  label: {
    fontSize: 11,
    fontWeight: '600',
    color: '#6B7280',
    marginBottom: 4,
  },
  time: {
    fontSize: 18,
    fontWeight: '700',
    color: '#1F2937',
  },
  date: {
    fontSize: 12,
    fontWeight: '500',
    color: '#6B7280',
    marginTop: 4,
  },
  selectedText: {
    color: '#FFFFFF',
  },
  unavailable: {
    opacity: 0.4,
  },
  unavailableText: {
    color: '#9CA3AF',
  },
  pressed: {
    opacity: 0.8,
  },
  checkIcon: {
    position: 'absolute',
    top: 8,
    right: 8,
  },
});