import React, { useEffect, useRef } from 'react';
import { View, Text, StyleSheet, Animated, ViewStyle} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { tokens } from '../../core/theme/tokens';

interface ProgressBarProps {
  progress: number; // 0-100
  showLabel?: boolean;
  height?: 'small' | 'medium' | 'large';
  variant?: 'default' | 'gradient' | 'success' | 'warning';
  animated?: boolean;
  style?: ViewStyle;
}

export const ProgressBar: React.FC<ProgressBarProps> = ({
  progress,
  showLabel = false,
  height = 'medium',
  variant = 'gradient',
  animated = true,
  style,
}) => {
  const widthAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (animated) {
      Animated.timing(widthAnim, {
        toValue: progress,
        duration: 1000,
        useNativeDriver: false,
      }).start();
    } else {
      widthAnim.setValue(progress);
    }
  }, [progress]);

  const renderFill = () => {
    const fillStyle = {
      width: animated 
        ? widthAnim.interpolate({
            inputRange: [0, 100],
            outputRange: ['0%', '100%'],
          })
        : `${progress}%`,
    };

    if (variant === 'gradient') {
      return (
        <Animated.View style={[styles.fill, fillStyle]}>
          <LinearGradient
            colors={['#445EFB', '#6EA8FF']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
            style={StyleSheet.absoluteFillObject}
          />
        </Animated.View>
      );
    }

    return (
      <Animated.View style={[
        styles.fill,
        fillStyle,
        { backgroundColor: variantColors[variant] },
      ]} />
    );
  };

  return (
    <View style={[styles.container, style]}>
      {showLabel && (
        <Text style={styles.label}>{Math.round(progress)}%</Text>
      )}
      <View style={[styles.track, heightStyles[height]]}>
        {renderFill()}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
  label: {
    fontSize: 12,
    fontWeight: '600',
    color: tokens.colors.textSecondary,
    marginBottom: 6,
  },
  track: {
    backgroundColor: '#E5E7EB',
    borderRadius: 8,
    overflow: 'hidden',
  },
  fill: {
    height: '100%',
    borderRadius: 8,
  },
});

const heightStyles = {
  small: { height: 4 },
  medium: { height: 8 },
  large: { height: 12 },
};

const variantColors = {
  default: tokens.colors.primary,
  gradient: '', // handled separately
  success: tokens.colors.success,
  warning: tokens.colors.warning,
};