import React from 'react';
import { View, Text, StyleSheet, ViewStyle, Platform } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';

interface StatCardProps {
  label: string;
  value: string | number;
  subValue?: string;
  icon?: keyof typeof Ionicons.glyphMap;
  trend?: 'up' | 'down' | 'neutral';
  trendValue?: string;
  variant?: 'default' | 'gradient' | 'outlined';
  style?: ViewStyle;
}

export const StatCard: React.FC<StatCardProps> = ({
  label,
  value,
  subValue,
  icon,
  trend,
  trendValue,
  variant = 'default',
  style,
}) => {
  const renderContent = () => (
    <>
      <View style={styles.header}>
        {icon && (
          <View style={styles.iconWrapper}>
            <Ionicons
              name={icon}
              size={24}
              color={variant === 'gradient' ? '#FFFFFF' : '#445EFB'}
            />
          </View>
        )}
        <Text style={[
          styles.label,
          variant === 'gradient' && styles.whiteText,
        ]}>
          {label}
        </Text>
      </View>
      
      <View style={styles.valueSection}>
        <Text style={[
          styles.value,
          variant === 'gradient' && styles.whiteText,
        ]}>
          {value}
        </Text>
        {subValue && (
          <Text style={[
            styles.subValue,
            variant === 'gradient' && styles.whiteSubText,
          ]}>
            {subValue}
          </Text>
        )}
      </View>
      
      {trend && (
        <View style={styles.trendSection}>
          <Ionicons
            name={trend === 'up' ? 'trending-up' : trend === 'down' ? 'trending-down' : 'remove'}
            size={16}
            color={trend === 'up' ? '#10B981' : trend === 'down' ? '#EF4444' : '#6B7280'}
          />
          {trendValue && (
            <Text style={[
              styles.trendValue,
              trend === 'up' && styles.trendUp,
              trend === 'down' && styles.trendDown,
            ]}>
              {trendValue}
            </Text>
          )}
        </View>
      )}
    </>
  );

  if (variant === 'gradient') {
    return (
      <LinearGradient
        colors={['#445EFB', '#6EA8FF']}
        style={[styles.card, style]}
      >
        {renderContent()}
      </LinearGradient>
    );
  }

  return (
    <View style={[
      styles.card,
      variant === 'outlined' && styles.outlined,
      style,
    ]}>
      {renderContent()}
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    padding: 16,
    borderRadius: 16,
    backgroundColor: '#FFFFFF',
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.08,
        shadowRadius: 8,
      },
      android: {
        elevation: 3,
      },
    }),
  },
  outlined: {
    borderWidth: 1.5,
    borderColor: '#E5E7EB',
    backgroundColor: 'transparent',
    shadowOpacity: 0,
    elevation: 0,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  iconWrapper: {
    marginRight: 8,
  },
  label: {
    fontSize: 14,
    fontWeight: '600',
    color: '#6B7280',
  },
  valueSection: {
    marginBottom: 8,
  },
  value: {
    fontSize: 28,
    fontWeight: '800',
    color: '#1F2937',
  },
  subValue: {
    fontSize: 12,
    fontWeight: '500',
    color: '#6B7280',
    marginTop: 2,
  },
  trendSection: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  trendValue: {
    fontSize: 12,
    fontWeight: '600',
    color: '#6B7280',
  },
  trendUp: {
    color: '#10B981',
  },
  trendDown: {
    color: '#EF4444',
  },
  whiteText: {
    color: '#FFFFFF',
  },
  whiteSubText: {
    color: 'rgba(255, 255, 255, 0.8)',
  },
});