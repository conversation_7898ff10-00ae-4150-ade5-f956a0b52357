// src/components/ui/SettingsRow.tsx
import React, { useRef } from 'react';
import {
  View,
  Text,
  Pressable,
  StyleSheet,
  Switch,
  Animated,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import { tokens } from '../../core/theme/tokens';

interface SettingsRowProps {
  icon?: keyof typeof Ionicons.glyphMap;
  iconColor?: string;
  label: string;
  description?: string;
  value?: string;
  onPress?: () => void;
  showArrow?: boolean;
  toggle?: boolean;
  toggleValue?: boolean;
  onToggle?: (value: boolean) => void;
  destructive?: boolean;
  isLast?: boolean;
}

export const SettingsRow: React.FC<SettingsRowProps> = ({
  icon,
  iconColor,
  label,
  description,
  value,
  onPress,
  showArrow = true,
  toggle = false,
  toggleValue,
  onToggle,
  destructive = false,
  isLast = false,
}) => {
  const scaleAnim = useRef(new Animated.Value(1)).current;

  const handlePressIn = () => {
    Animated.spring(scaleAnim, {
      toValue: 0.98,
      useNativeDriver: true,
    }).start();
  };

  const handlePressOut = () => {
    Animated.spring(scaleAnim, {
      toValue: 1,
      useNativeDriver: true,
    }).start();
  };

  const handlePress = async () => {
    if (onPress && !toggle) {
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      onPress();
    }
  };

  const content = (
    <Animated.View
      style={[
        styles.container,
        !isLast && styles.withBorder,
        { transform: [{ scale: scaleAnim }] },
      ]}
    >
      <View style={styles.leftSection}>
        {icon && (
          <View
            style={[
              styles.iconWrapper,
              { backgroundColor: `${iconColor || tokens.colors.primary}15` },
            ]}
          >
            <Ionicons
              name={icon}
              size={22}
              color={iconColor || tokens.colors.primary}
            />
          </View>
        )}
        <View style={styles.textContainer}>
          <Text style={[styles.label, destructive && styles.destructiveText]}>
            {label}
          </Text>
          {description && (
            <Text style={styles.description}>{description}</Text>
          )}
        </View>
      </View>

      <View style={styles.rightSection}>
        {value && !toggle && (
          <Text style={styles.value}>{value}</Text>
        )}
        {toggle && (
          <Switch
            value={toggleValue}
            onValueChange={onToggle}
            trackColor={{
              false: tokens.colors.border,
              true: tokens.colors.primary,
            }}
            thumbColor="#FFFFFF"
          />
        )}
        {showArrow && !toggle && onPress && (
          <Ionicons
            name="chevron-forward"
            size={20}
            color={tokens.colors.textTertiary}
          />
        )}
      </View>
    </Animated.View>
  );

  if (onPress && !toggle) {
    return (
      <Pressable
        onPress={handlePress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
      >
        {content}
      </Pressable>
    );
  }

  return content;
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    paddingHorizontal: 20,
    backgroundColor: tokens.colors.backgroundPure,
    minHeight: 68,
  },
  withBorder: {
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: tokens.colors.border,
  },
  leftSection: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  iconWrapper: {
    width: 40,
    height: 40,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 14,
  },
  textContainer: {
    flex: 1,
  },
  label: {
    fontSize: 17,
    fontWeight: '600',
    color: tokens.colors.textPrimary,
    marginBottom: 2,
  },
  description: {
    fontSize: 13,
    color: tokens.colors.textTertiary,
    marginTop: 2,
  },
  destructiveText: {
    color: tokens.colors.error,
  },
  rightSection: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
  },
  value: {
    fontSize: 15,
    color: tokens.colors.textSecondary,
  },
});